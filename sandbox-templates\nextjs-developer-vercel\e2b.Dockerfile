# You can use most Debian-based base images
FROM node:22-slim

# Install curl
RUN apt-get update && apt-get install -y curl && apt-get clean && rm -rf /var/lib/apt/lists/*

COPY compile_page.sh /compile_page.sh
RUN chmod +x /compile_page.sh

COPY deploy.sh /deploy.sh
RUN chmod +x /deploy.sh

# Install dependencies and customize sandbox
WORKDIR /home/<USER>/nextjs-app

RUN npx create-next-app@next-14 . --js --tailwind --no-eslint --import-alias "@/*" --use-npm --no-app --no-src-dir --yes

RUN npx shadcn@latest init -d -y
RUN npx shadcn@latest add --all -y
RUN npm install vercel -g

