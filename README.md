### Node Version: 18
### Nextjs: 14

#### 项目命令：
- **开发** ：`npm run dev`
- **打包** ：`npm run build`
- **线上启动** ：`打包` & `npm run pm2`

#### 项目路由：
- **openai** ：`/v1/chat/openai`
- **claude** : `/v1/chat/claude`

#### Header参数 (所有模型都需要添加！！！)：
| 字段          | 类型   | 是否必需 | 默认值 | 描述                                     |
| ------------- | ------ | -------- | ------ | ---------------------------------------- |
| userid        | String | 是       | 无     | 需要基于userid创建沙盒实例               |
| Authorization | String | 是       | 无     | `Bearer<空格>` + `apiKey` （模型apiKey） |

#### POST Body默认参数:
###### * 根据各模型要求传参，传参相同则覆盖
- `openai`:
  ><br/> model: 'gpt-4-turbo-2024-04-09',
    <br/>
- `claude`:
  ><br/>max_tokens: 4096,
                model: 'claude-3-haiku-20240307',
    <br/>
