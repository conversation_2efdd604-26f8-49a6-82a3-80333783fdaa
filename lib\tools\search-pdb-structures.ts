import { Tool } from 'openai-function-calling-tools'
import { z } from 'zod';
import { toolInfoMap, TOOL_NAME } from './tool-info';
import { FunctionDefinition } from 'openai/resources';
import { TOOL_EXECUTION_MESSAGE } from '@/app/api/chat/common/constants';

function createSearchPdbStructures() {

  const toolName = TOOL_NAME.SEARCH_PDB_STRUCTURES;
  const paramsSchema = z.object({
    protein_name: z.string().describe(toolInfoMap.get(toolName)!.params.protein_name),
    organism: z.string().optional().describe(toolInfoMap.get(toolName)!.params.organism),
  });

  const execute = async ({ protein_name, organism }: z.infer<typeof paramsSchema>) => {

    try {
      // 构建RCSB搜索查询
      const queryNodes = [
        {
          type: "terminal",
          service: "full_text",
          parameters: {
            value: protein_name
          }
        }
      ];

      // 如果指定了生物体，添加生物体过滤条件
      if (organism) {
        queryNodes.push({
          type: "terminal",
          service: "text",
          parameters: {
            attribute: "rcsb_entity_source_organism.ncbi_scientific_name",
            operator: "exact_match",
            value: organism
          }
        });
      }

      const searchQuery = {
        query: {
          type: "group",
          logical_operator: "and",
          nodes: queryNodes
        },
        request_options: {
          paginate: {
            start: 0,
            rows: 10
          },
          sort: [
            {
              sort_by: "score",
              direction: "desc"
            }
          ]
        },
        return_type: "entry"
      };

      // 调用RCSB API
      const response = await fetch('https://search.rcsb.org/rcsbsearch/v2/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(searchQuery)
      });

      if (!response.ok) {
        throw new Error(`RCSB API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.result_set || data.result_set.length === 0) {
        return JSON.stringify({
          message: `No PDB structures found for protein "${protein_name}"${organism ? ` in organism "${organism}"` : ''}.`,
          pdb_ids: [],
          total_count: 0
        });
      }

      // 取前3个结果
      const topResults = data.result_set.slice(0, 3);
      const pdbIds = topResults.map((result: any) => ({
        pdb_id: result.identifier,
        score: result.score
      }));

      return JSON.stringify({
        message: `Found ${data.total_count} PDB structures for "${protein_name}"${organism ? ` in "${organism}"` : ''}. Returning top 3 results.`,
        pdb_ids: pdbIds,
        total_count: data.total_count,
        search_query: {
          protein_name,
          organism: organism || 'All organisms'
        }
      });

    } catch (error) {
      console.log('PDB search error---->', error);
      return JSON.stringify({
        message: TOOL_EXECUTION_MESSAGE,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;

}

export { createSearchPdbStructures };
