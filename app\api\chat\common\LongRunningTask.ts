import EventEmitter from "events";

export abstract class LongRunningTask<P> extends EventEmitter {
    protected param: P;
    protected shouldContinue: boolean;
    protected canceled: boolean;
    protected searchResult: string;
    protected jsonResult: any;
    protected relatedSearchQueries: any[];
    protected messageInReturn: string;
    protected count: number;
    constructor(
        param: P,
    ) {
        super();
        this.param = param;
        this.shouldContinue = true;
        this.canceled = false;

        this.searchResult = '';
        this.jsonResult = {};
        this.relatedSearchQueries = [];
        this.messageInReturn = '';
        this.count = 0;
    }
    abstract run(): Promise<void>;
    async cancel(): Promise<void> {
        this.canceled = true;
    }

}