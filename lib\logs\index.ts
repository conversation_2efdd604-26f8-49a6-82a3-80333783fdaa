export interface LogParams {
    type: string;
    message: any;
}

export default async function log(log: LogParams, consoleInTerminal = true) {
    try {
        const { type, message } = log;

        if (consoleInTerminal) {
            console.log("-------------LOG IN TERMINAL-------------------")
            console.log(JSON.stringify({ time: new Date().toLocaleString(), type, message }));
        }

        const logData = {
            type,
            message: JSON.stringify(message),
        };

        const body = JSON.stringify(logData);

        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.LOG_SECRET_KEY}`
        };

        await fetch(process.env.LOG_URL!, {
            method: 'POST',
            headers,
            body
        });

    } catch (error) {
        // console.log('ADD LOG ERROR:', error);
    }

}