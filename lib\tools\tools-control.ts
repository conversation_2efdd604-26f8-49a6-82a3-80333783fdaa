import { TOOLS_CONTROL } from "@/lib/core/enum";
import { TOOL_NAME } from "@/lib/tools/tool-info";

export interface ToolControl {
    name: string;
    id: TOOLS_CONTROL;
    tools: string[];
}

export type ToolControlParam = Pick<ToolControl, 'id' | 'name'>;


export const toolsControl: ToolControl[] = [
    {
        name: 'DALL·E3',
        id: TOOLS_CONTROL.DALL_E3,
        tools: [
            TOOL_NAME.CREATE_IMAGES
        ]
    },
    {
        name: 'Mermaid',
        id: TOOLS_CONTROL.MERMAID,
        tools: [
            TOOL_NAME.GENERATE_MERMAID_CHARTS
        ]
    },
    {
        name: 'Browsing',
        id: TOOLS_CONTROL.BROWSING,
        tools: [
            TOOL_NAME.SEARCH_ONLINE,
            TOOL_NAME.SEARCH_BY_URL,
        ]
    },
    {
        name: 'Code Interprer',
        id: TOOLS_CONTROL.CODE_INTERPRER,
        tools: [
            TOOL_NAME.RUN_PYTHON_CODE,
            TOOL_NAME.UPLOAD_FILE_TO_SANDBOX,
        ]
    },
    {
        name: 'Advanced analysis',
        id: TOOLS_CONTROL.ADVANCED_ANALYSIS,
        tools: [
            TOOL_NAME.RUN_PYTHON_CODE,
            TOOL_NAME.UPLOAD_FILE_TO_SANDBOX,
        ]
    },
    {
        name: '𝕏',
        id: TOOLS_CONTROL.X,
        tools: [
            TOOL_NAME.GET_X_TWITTER_INFO
        ]
    },
    {
        name: 'Chemistry',
        id: TOOLS_CONTROL.CHEMISTRY,
        tools: [
            TOOL_NAME.GENERATE_SMILES_FROM_MOLECULE
        ]
    },
]


