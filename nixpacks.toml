[phases.setup]
nixPkgs = [
  # Node.js
  "nodejs_18",
  # pnpm package manager
  "nodePackages.pnpm", # Chromium and related dependencies
  "chromium",
  "xorg.libX11",
  "xorg.libXcomposite",
  "xorg.libXdamage",
  "xorg.libXext",
  "xorg.libXfixes",
  "xorg.libXrandr",
  "xorg.libxcb",
  "libdrm",
  "mesa",
  "pango",
  "cairo",
  "alsa-lib",
  "nss",
  "nspr",
  "atk",
  "at-spi2-atk",
  "dbus",
  "cups",
  "expat",
  "libxkbcommon", # Font support
  "wqy_zenhei",
  "noto-fonts",
  "noto-fonts-cjk-sans",
]
[phases.install]
cmds = [
  # Verify pnpm installation
  "pnpm --version", # Set registry mirror
  "pnpm config set registry https://registry.npmmirror.com", # Install Chrome
  "mkdir -p /root/.cache/puppeteer",
  "PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true pnpm install", # Create Chrome symbolic links
  "ln -sf $(which chromium) /usr/bin/chromium",
  "ln -sf $(which chromium) /usr/bin/google-chrome",
  "ls -la /usr/bin/chromium", # Verify the symlink
  "ls -la /usr/bin/google-chrome", # Verify the symlink
]

[phases.build]
cmds = [
  "pnpm run build:${ENV}",
]

[start]
cmd = "pnpm start:${ENV}"

[env]
# Node environment
NODE_ENV = "${ENV}"

# Puppeteer configuration
PUPPETEER_EXECUTABLE_PATH = "/usr/bin/chromium"
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD = "true"

# Disable sandbox (required in some environments)
PUPPETEER_NO_SANDBOX = "true"

# Cache path
PUPPETEER_CACHE_DIR = "/root/.cache/puppeteer"

# pnpm configuration
PATH = "/usr/bin:/pnpm:$PATH"
PNPM_HOME = "/pnpm"

# Timezone
TZ = "Asia/Shanghai"
