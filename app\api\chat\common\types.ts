import Anthropic from "@anthropic-ai/sdk";
import { MessageCreateParamsBase, RawMessageStreamEvent } from "@anthropic-ai/sdk/resources/messages";
import { Stream } from "@anthropic-ai/sdk/streaming";
import { Stream as OpenAIStream } from "openai/streaming";
import OpenAI from "openai";
import { ChatCompletionChunk, ChatCompletionCreateParamsBase } from "openai/resources/chat/completions.mjs";

export interface Notify {
    log: (message: string) => void;
    complete: (data: any) => void;
    error: (error: Error | any) => void;
    close: () => void;
}

export interface AnthropicTaskParams {
    body: MessageCreateParamsBase,
    userID: string,
    client: Anthropic,
    response: Stream<RawMessageStreamEvent>,
}


export interface OpenAITaskParams {
    body: ChatCompletionCreateParamsBase,
    userID: string,
    client: OpenAI,
    response: OpenAIStream<ChatCompletionChunk>,
}


export interface CallResultModel {
    data: any,
    ctrl?: <PERSON>bor<PERSON><PERSON><PERSON>roller
}

