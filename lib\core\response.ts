import { NextResponse } from "next/server";
import { ResponseRes } from "@/lib/types/responseModel";
import { HttpStatusCode } from "axios";

export function success<T>(data: T) {
    const res: ResponseRes<T> = {
        code: HttpStatusCode.Ok,
        success: true,
        data
    };
    return NextResponse.json(
        res, { status: HttpStatusCode.Ok }
    );
}

export function fail(msg = '', code = HttpStatusCode.InternalServerError, statusCode = HttpStatusCode.Ok) {
    const res: ResponseRes<null> = {
        code,
        success: false,
        msg,
        data: null
    };
    return NextResponse.json(
        res, { status: statusCode }
    );
}
