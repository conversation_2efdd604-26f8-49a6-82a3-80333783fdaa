import { Tool } from 'openai-function-calling-tools'
import { z } from 'zod';
import { toolInfoMap, TOOL_NAME } from './tool-info';
import { TOOL_EXECUTION_MESSAGE } from '@/app/api/chat/common/constants';
import EventEmitter from 'events';
import Anthropic from '@anthropic-ai/sdk';
import { parsePartialJson } from '@ai-sdk/ui-utils';
import { PartialObject } from 'lodash';


async function generateArtifacts(prompt: string, htmlEvent: EventEmitter, historyMessages: any[]) {
  const client = new Anthropic({
    apiKey: process.env.ANTHROPIC_API_KEY!,
    baseURL: process.env.ANTHROPIC_BASE_URL,
    dangerouslyAllowBrowser: true,
  });

  const prefix = `<!DOCTYPE html>`;
  let content = prefix;

  const cleanedHistoryMessages = historyMessages.slice(0, -1).map((message) => ({
    role: message.role,
    content: message.content
  }))
  const artifacts = historyMessages
    .filter(message => message.role === 'assistant')
    .reduce((ats: any[], message: any) => {
      if (message.artifactsList?.length) {
        return [...ats, ...message.artifactsList]
      }
      return ats
    }, []);
  const systemPrompt = `you are a helpful assistant that can generate artifacts based on the user's input. Must follow the instructions below:
- MUST use Tailwind CSS for styling.
- MUST use CDN for loading JS and CSS resources.
- MUST get the image from unsplash when you need an image in a web page
- MUST use interactive chart libraries, such as echarts, Chart.js, and D3.js for charts.
- MUST use three.js for 3D scenes when user request to generate 3D scenes.
    - the texture of the 3D scene should be from Official resources and examples ( https://threejs.org/examples/ )
    - the 3D scene should be interactive
- MUST return HTML code starting with <!DOCTYPE html> and ending with </html>
- MUST to assign a unique UUID to each HTML element as its id
- MUST refer to the history messages as the context for the output. Because sometimes the previous HTML will be modified. If it is modified, try to only modify the parts that the user intends to change.
- MUST carefully verify whether the user is requesting to modify the existing artifacts:
- if the user want to modify/tweak the artifact with the specific id, you should modify the part of the html code that the user wants to modify.
- if the user want to modify/tweak the artifact ,but no specific id is provided, you should use the last artifact that with the id: ${artifacts[artifacts.length - 1]?.id}
`;


  const response = await client.messages.create({
    system: systemPrompt,
    model: "claude-sonnet-4-20250514",
    messages: [
      { role: "user", content: `${prompt}\n\n Here is the history artifacts with json format: ${JSON.stringify(artifacts)}` },
      { role: "assistant", content: prefix }
    ],
    max_tokens: 64000,
    stream: true,
    temperature: 0,
  });
  console.log('artifacts response---->', response)

  for await (const part of response) {
    // console.log('artifacts part---->', JSON.stringify(part))
    if (part.type === 'content_block_delta' && part.delta.type === 'text_delta') {
      content += part.delta.text;
      htmlEvent.emit('progress', content);
    }
  }

  console.log("artifacts complete !!!!");
  htmlEvent.emit('complete', content);
  return content;
}

async function generateArtifactInfo(prompt: string, infoEvent: EventEmitter): Promise<{ title: string, description: string } | undefined> {
  const client = new Anthropic({
    apiKey: process.env.ANTHROPIC_API_KEY!,
    baseURL: process.env.ANTHROPIC_BASE_URL,
    dangerouslyAllowBrowser: true,
  });

  const prefix = `{"title":`;
  let returnContent = prefix;

  const systemPrompt = `You are an artifact info generator. Generate a concise title and description for the given artifact in JSON format.
- The title MUST be short (max 3 words)
- The description MUST be a short sentence (max 1 sentence)
- MUST Return valid JSON with "title" and "description" fields
- MUST Start with \`{"title":\` and end with \`"}\`
for example:\`{"title": "A beautiful sunset over the ocean", "description": "A stunning view of the ocean with a beautiful sunset"}\`
`;

  try {
    const response = await client.messages.create({
      system: systemPrompt,
      model: "claude-3-5-haiku-20241022",
      messages: [
        { role: "user", content: prompt },
        { role: "assistant", content: prefix }
      ],
      max_tokens: 1000,
      stream: true,
      temperature: 1,
    });

    let parsed = { title: '', description: '' }

    for await (const part of response) {
      if (part.type === 'content_block_delta' && part.delta.type === 'text_delta') {
        returnContent += part.delta.text;
        try {
          const parsedContent = parsePartialJson(returnContent);
          if (parsedContent.value) {
            parsed = parsedContent.value as { title: string, description: string }
            infoEvent.emit('progress', parsed);
          }
        } catch {
          // Continue if JSON is not complete yet
        }
      }
    }

    infoEvent.emit('complete', parsed);
    return parsed;


  } catch (error) {
    console.error('Error generating artifact info:', error);
    return undefined;
  }
}

function createGenerateArtifacts() {

  const toolName = TOOL_NAME.GENERATE_ARTIFACTS;
  const paramsSchema = z.object({
    prompt: z.string().describe(toolInfoMap.get(toolName)!.params.prompt),
    artifactEvent: z.instanceof(EventEmitter).describe(toolInfoMap.get(toolName)!.params.artifactEvent),
    historyMessages: z.array(z.any()).describe(toolInfoMap.get(toolName)!.params.historyMessages).default([]),
  });



  const execute = async ({ prompt, artifactEvent, historyMessages }: z.infer<typeof paramsSchema>) => {
    try {
      const htmlEvent = new EventEmitter();
      const infoEvent = new EventEmitter();
      const info: any = {};

      // Set up event handlers to sync progress between events
      const updateProgress = (data: any, done: boolean = false) => {
        Object.assign(info, data);
        if (done) {
          artifactEvent.emit('complete', info);
        } else {
          artifactEvent.emit('progress', info);
        }
      };

      infoEvent.on('progress', (data: PartialObject<{ title: string, description: string }>) => {
        updateProgress(data);
      });

      htmlEvent.on('progress', (html: string) => {
        updateProgress({ html });
      });

      htmlEvent.on('complete', (html: string) => {
        updateProgress({ html }, true);
      });

      // Execute artifact generation in parallel
      await Promise.all([
        generateArtifacts(prompt, htmlEvent, historyMessages),
        generateArtifactInfo(prompt, infoEvent)
      ]);

      return JSON.stringify({
        msg: "Artifact generated successfully.",
        data: {
          title: info.title || '',
          description: info.description || ''
        }
      });

    } catch (error) {
      console.error('Error generating artifacts:', error);
      return TOOL_EXECUTION_MESSAGE;
    }
  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;

}

export { createGenerateArtifacts };