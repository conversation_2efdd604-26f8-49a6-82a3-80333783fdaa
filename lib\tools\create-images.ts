import { Tool } from 'openai-function-calling-tools';
import { z } from 'zod';
import { OpenAI } from 'openai';
import { writeFile } from "fs/promises";
import { toolInfoMap, TOOL_NAME } from './tool-info';
import { uploadToOSS } from '../core/upload-to-oss';
import { v4 as uuidv4 } from 'uuid';
function createImagesSchemaByprompt() {

  const toolName = TOOL_NAME.CREATE_IMAGES;
  const paramsSchema = z.object({
    prompt: z.string().describe(toolInfoMap.get(toolName)!.params.prompt),
  });

  const execute = async ({ prompt }: z.infer<typeof paramsSchema>) => {

    try {

      const client = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_BASE_URL,
        dangerouslyAllowBrowser: true
      });

      const img = await client.images.generate({
        model: "gpt-image-1",
        prompt: prompt,
        n: 1,
        size: "1024x1024"
      });

      if (img?.data?.[0]?.b64_json) {
        const tmpFilePath = `/tmp/${uuidv4()}.png`;
        const imageBuffer = Buffer.from(img?.data?.[0]?.b64_json, "base64");
        await writeFile(tmpFilePath, imageBuffer);

        const fileLink = await uploadToOSS({ filePath: tmpFilePath, remotePath: 'create-images' });

        return { imageURL: fileLink };

      } else {

        return { imageURL: '' };

      }
    } catch (error) {
      console.error('error---', error);
      return { imageURL: '' };
    }

  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;
}

export { createImagesSchemaByprompt };