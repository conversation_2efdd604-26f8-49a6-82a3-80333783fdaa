import { createAnthropic } from '@ai-sdk/anthropic'
import { createOpenAI } from '@ai-sdk/openai'

export type LLMModel = {
  id: string
  name: string
  provider: string
}

export type LLMModelConfig = {
  model?: string
  apiKey?: string
  baseURL?: string
  temperature?: number
  topP?: number
  topK?: number
  frequencyPenalty?: number
  presencePenalty?: number
  maxTokens?: number
}

export function getModelClient(modelNameString: string, config: LLMModelConfig) {
  const { apiKey, baseURL } = config

  const providerId = modelNameString.startsWith('claude') ? 'anthropic' : 'openai'

  const providerConfigs = {
    anthropic: () => createAnthropic({ apiKey, baseURL })(modelNameString),
    openai: () => createOpenAI({ apiKey, baseURL })(modelNameString),
  }

  const createClient =
    providerConfigs[providerId as keyof typeof providerConfigs]

  if (!createClient) {
    throw new Error(`Unsupported provider: ${providerId}`)
  }

  return createClient()
}

export function getDefaultMode(model: LLMModel) {
  // const { id: modelNameString } = model

  // // monkey patch fireworks
  // if (providerId === 'fireworks') {
  //   return 'json'
  // }

  return 'auto'
}
