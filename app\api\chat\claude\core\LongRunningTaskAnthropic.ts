import { AnthropicTaskParams } from "@/app/api/chat/common/types";
import { getCompletion } from "./completion";
import { Stream } from "@anthropic-ai/sdk/streaming";
import callTool from "@/app/api/chat/common/call-tool";
import { CONNECTION_SYMBOL, MAX_CONVERSATION_LOOP, MERMAID_SUCCESS_MESSAGE, TOOL_EXECUTION_MESSAGE } from "@/app/api/chat/common/constants";
import { getRelatedSearchQuery } from "../../common/get-related-search-query";
import { MessageParam, RawMessageStreamEvent, ThinkingBlock, ToolUseBlock } from "@anthropic-ai/sdk/resources/messages";
import { TOOL_NAME, toolNameMapping } from "@/lib/tools/tool-info";
import log from "@/lib/logs";
import { LongRunningTask } from "../../common/LongRunningTask";
import { AbortControllerContainer } from "@/lib/core/abort-controller";
import { TimeoutError } from "../../common/Errors";
import { ToolResultBlockParam } from "@anthropic-ai/sdk/resources/messages";
import EventEmitter from "events";

// Define the type for tool input
interface ToolInput {
    files?: string | any[];
    [key: string]: any;
}

// Extend the ToolUseBlock type to include our custom input type
interface ExtendedToolUseBlock extends Omit<ToolUseBlock, 'input'> {
    input: ToolInput;
}

export class LongRunningTaskAnthropic extends LongRunningTask<AnthropicTaskParams> {
    toolUse: boolean;
    toolBlock: ExtendedToolUseBlock | undefined;
    artifactsCount: number;
    callResult: string;
    tmpMessage: string;
    constructor(
        param: AnthropicTaskParams,
    ) {
        super(param);
        this.toolUse = false;
        this.toolBlock = undefined;
        this.artifactsCount = -1;
        this.callResult = '';
        this.tmpMessage = '';
    }

    toolCallEnd() {
        if (this.toolUse && this.toolBlock) {
            this.emit('progress', `data: ${JSON.stringify({
                "type": "content_block_delta",
                "JUCHATS_ACTION_MSG": {
                    type: "TOOL_CALL_END",
                    toolName: toolNameMapping[this.toolBlock?.name as TOOL_NAME]
                }
            })}\n\n`);

            if (this.toolBlock?.name == TOOL_NAME.GENERATE_MERMAID_CHARTS && this.callResult) {
                const data = JSON.parse(this.callResult);
                if (data.message == MERMAID_SUCCESS_MESSAGE) {
                    this.emit('progress', `data: ${JSON.stringify({
                        "type": "content_block_delta",
                        "JUCHATS_ACTION_MSG": {
                            type: "TOOL_CALL_CONTENT",
                            toolName: toolNameMapping[this.toolBlock?.name as TOOL_NAME],
                            content: "\n```mermaid\n" + data.mermaidCode + "\n```\n"
                        }
                    })}\n\n`);
                }
            }
            if (this.toolBlock?.name == TOOL_NAME.GENERATE_SMILES_FROM_MOLECULE && this.callResult) {
                try {
                    const smilesData = JSON.parse(this.callResult);
                    if (smilesData.smiles) {
                        const formattedSmiles = "\n```smiles\n" + smilesData.smiles + "\n```\n";
                        this.callResult = formattedSmiles; // Override callResult
                        this.emit('progress', `data: ${JSON.stringify({
                            "type": "content_block_delta",
                            "JUCHATS_ACTION_MSG": {
                                type: "TOOL_CALL_CONTENT",
                                toolName: toolNameMapping[this.toolBlock?.name as TOOL_NAME],
                                content: formattedSmiles
                            }
                        })}\n\n`);
                    }
                } catch {

                }
            }
            if (this.toolBlock?.name == TOOL_NAME.SEARCH_PDB_STRUCTURES && this.callResult) {
                try {
                    const pdbData = JSON.parse(this.callResult);
                    if (pdbData.pdb_ids && pdbData.pdb_ids.length > 0) {
                        // 生成多个<juchats-pdb>标签
                        const pdbTags = pdbData.pdb_ids.map((item: any) =>
                            `<juchats-pdb>${item.pdb_id}</juchats-pdb>`
                        ).join('\n');
                        const formattedPdb = `\n${pdbTags}\n`;
                        this.callResult = formattedPdb; // Override callResult
                        this.emit('progress', `data: ${JSON.stringify({
                            "type": "content_block_delta",
                            "JUCHATS_ACTION_MSG": {
                                type: "TOOL_CALL_CONTENT",
                                toolName: toolNameMapping[this.toolBlock?.name as TOOL_NAME],
                                content: formattedPdb
                            }
                        })}\n\n`);
                    }
                } catch {

                }
            }

            // 如果工具是生成artifacts，artifacts结束
            if (this.toolBlock?.name == TOOL_NAME.GENERATE_ARTIFACTS && this.callResult && this.callResult != TOOL_EXECUTION_MESSAGE) {
                const data = JSON.parse(this.callResult).data;
                data.index = this.artifactsCount;
                data.reference = `The ${this.artifactsCount}-th element in the artifacts json at the end of the text`;
                this.emit('progress', `data: ${JSON.stringify({
                    "type": "content_block_delta",
                    "JUCHATS_ACTION_MSG": {
                        type: "TOOL_CALL_CONTENT",
                        toolName: toolNameMapping[this.toolBlock?.name as TOOL_NAME],
                        content: `\n\n<juchats-artifacts>${JSON.stringify(data)}</juchats-artifacts>\n\n`
                    }
                })}\n\n`);
            }
            this.toolUse = false;
            this.toolBlock = undefined;
        }
    }

    async run(): Promise<void> {

        let { body, response, userID, client } = this.param;

        let toolMessages: MessageParam[] = [];

        let toolMessagesData: MessageParam[] = [];
        const messages: MessageParam[] = body.messages;
        let thinkBlock: ThinkingBlock | undefined;
        let artifactEvent: EventEmitter | undefined;

        while (this.shouldContinue && this.count < MAX_CONVERSATION_LOOP) {


            this.toolCallEnd();
            this.tmpMessage = '';

            if (this.canceled) {
                return;
            }
            this.count++;
            console.log('this.count----->，userID:', userID, this.count);

            let isThinking = false;

            let partMessage = '';

            let finalJsonString = '';
            for await (const part of response) {
                if (userID == '1111') {
                    console.log('part----->', 'userID:', userID, 'count:', this.count, JSON.stringify(part));
                }
                switch (part.type) {
                    case 'content_block_start':
                        const { content_block } = part;
                        if (content_block.type == 'tool_use') {
                            this.toolBlock = content_block as ExtendedToolUseBlock;
                            this.toolUse = true;
                            
                            this.emit('progress', `data: ${JSON.stringify({
                                "type": "content_block_delta",
                                "JUCHATS_ACTION_MSG": {
                                    type: "CHOOSE_TOOLS"
                                }
                            })}\n\n`)
                        } else if (content_block.type == 'thinking') {
                            isThinking = true;
                            thinkBlock = content_block;
                            this.emit('progress', `data: ${JSON.stringify(part)}\n\n`)
                            this.emit('progress', `data: ${JSON.stringify({
                                type: 'content_block_delta',
                                index: part.index,
                                delta: {
                                    type: 'text_delta',
                                    text: `\n\n<juchats-thinking>\n\n`
                                }
                            })}\n\n`)
                        }
                        break;
                    case 'content_block_delta':
                        const { delta } = part;
                        if (delta.type == 'input_json_delta') {
                            finalJsonString += delta.partial_json;
                        } else if (delta.type == 'text_delta') {

                            this.emit('progress', `data: ${JSON.stringify(part)}\n\n`)
                            this.messageInReturn += delta.text;
                            this.tmpMessage += delta.text;
                            partMessage += delta.text;
                        } else {
                            let newPart = part
                            if (delta.type == 'thinking_delta') {
                                //这里将 thinking 转成普通消息返回
                                newPart = {
                                    ...part,
                                    delta: {
                                        type: 'text_delta',
                                        text: delta.thinking
                                    }
                                }

                                //将 thinking 拼到thinkBlock
                                if (thinkBlock) {
                                    thinkBlock.thinking += delta.thinking;
                                }

                            } else if (delta.type == 'signature_delta') {
                                if (thinkBlock) {
                                    thinkBlock.signature = delta.signature;
                                    isThinking = false;
                                    this.emit('progress', `data: ${JSON.stringify({
                                        type: 'content_block_delta',
                                        index: part.index,
                                        delta: {
                                            type: 'text_delta',
                                            text: "\n\n</juchats-thinking>\n\n"
                                        }
                                    })}\n\n`)
                                }
                            }
                            this.emit('progress', `data: ${JSON.stringify(newPart)}\n\n`)
                        }
                        break;
                    case 'content_block_stop':
                        this.emit('progress', `data: ${JSON.stringify(part)}\n\n`)
                        break;
                    case 'message_delta':

                        this.emit('progress', `data: ${JSON.stringify(part)}\n\n`)
                        console.log('stop_reason----->', part.delta.stop_reason);

                        break;
                    case 'message_stop':
                        if (finalJsonString && this.toolBlock) {
                            try {
                                this.toolBlock.input = JSON.parse(finalJsonString);
                                if (this.toolBlock.input?.files && typeof this.toolBlock.input.files === 'string') {
                                    this.toolBlock.input.files = JSON.parse(this.toolBlock.input.files);
                                }
                            } catch (error) {
                                log({
                                    type: "ERROR",
                                    message: {
                                        model: 'Claude',
                                        message: `parse tool input error:${finalJsonString}`,
                                        userID,
                                    },
                                })
                            }

                            const toolName = toolNameMapping[this.toolBlock.name as TOOL_NAME]
                            console.log('toolName---->', toolName)
                            this.emit('progress', `data: ${JSON.stringify({
                                "type": "content_block_delta",
                                "JUCHATS_ACTION_MSG": {
                                    type: "TOOL_CALL_START",
                                    toolName
                                }
                            })}\n\n`)


                            try {
                                if (this.toolBlock.name === TOOL_NAME.GENERATE_ARTIFACTS) {
                                    this.artifactsCount++;
                                    artifactEvent = new EventEmitter();
                                    artifactEvent.on('progress', (data: any) => {
                                        this.emit('progress', `data: ${JSON.stringify({ artifacts: data })}\n\n`)
                                    })
                                    artifactEvent.on('complete', (data: any) => {
                                        this.emit('progress', `data: ${JSON.stringify({ artifacts: data, done: true })}\n\n`)
                                    })
                                }
                                const startTime = Date.now();
                                const result = await callTool(this.toolBlock.name as TOOL_NAME, this.toolBlock.input, userID, artifactEvent, messages);
                                const executionTime = Date.now() - startTime;
                                console.log(`Tool execution time for ${this.toolBlock.name}: ${executionTime}ms`);
                                this.callResult = result.callResult;
                                const uuid = result.uuid;
                                log({
                                    type: "TOOL_CALL",
                                    message: {
                                        type: 'tool_call_result',
                                        uuid,
                                        model: 'Claude',
                                        tool: this.toolBlock.name,
                                        callResult: this.callResult,
                                        userID,
                                    },
                                })
                            } catch (error) {
                                console.log('tool call error---->', error)
                                if (this.toolBlock.name === TOOL_NAME.GENERATE_ARTIFACTS) {
                                    this.artifactsCount--;
                                }
                            }


                            if (this.toolBlock.name == TOOL_NAME.SEARCH_ONLINE && this.callResult && this.callResult != TOOL_EXECUTION_MESSAGE) {

                                try {
                                    // Parse existing and new search results
                                    const parsedResults = this.searchResult
                                        ? [...JSON.parse(this.searchResult), ...JSON.parse(this.callResult)]
                                        : JSON.parse(this.callResult);

                                    // Remove contexts and snippets from results if they exist
                                    const cleanedResults = parsedResults.map(({ contexts, ...rest }: { contexts: any, rest: any }) => rest);

                                    cleanedResults.forEach((item: any, index: number) => {
                                        item.position = index + 1;
                                    })
                                    // Update search results
                                    this.searchResult = JSON.stringify(cleanedResults);
                                } catch (error) {

                                }
                                try {
                                    this.relatedSearchQueries = await getRelatedSearchQuery({ messages })
                                } catch (error) {
                                    this.relatedSearchQueries = [];
                                    log({
                                        type: "ERROR",
                                        message: {
                                            model: 'Claude',
                                            message: `getRelatedSearchQuery error:${error}`,
                                            userID,
                                        },
                                    })
                                }
                            }

                            let callResultJSON;

                            if (this.toolBlock.name == TOOL_NAME.RUN_PYTHON_CODE) {
                                try {
                                    callResultJSON = JSON.parse(this.callResult);
                                } catch (error) {
                                    callResultJSON = {
                                        message: "Failed to parse Python code execution result please try again",
                                        charts_images: []
                                    };
                                }
                            }

                            if (this.toolBlock.name == TOOL_NAME.GENERATE_MERMAID_CHARTS) {
                                try {
                                    callResultJSON = JSON.parse(this.callResult);
                                } catch (error) {
                                    callResultJSON = {
                                        message: TOOL_EXECUTION_MESSAGE,
                                    };
                                }
                            }

                            const toolcallContent: any = []
                            if (thinkBlock) {
                                toolcallContent.push(thinkBlock)
                                thinkBlock = undefined;
                            }

                            if (
                                !body.model.startsWith("claude-3-7-sonnet-") &&
                                !body.model.startsWith("claude-sonnet-4") &&
                                !body.model.startsWith("claude-opus-4")
                            ) {

                                if (partMessage) {
                                    toolcallContent.unshift(
                                        {
                                            type: "text",
                                            text: partMessage,
                                        }
                                    )

                                } else {
                                    toolcallContent.unshift(
                                        {
                                            type: "text",
                                            text: `I will call the tool \`${this.toolBlock.name}\` to get the information，
                                            If tool is \`${TOOL_NAME.GENERATE_MERMAID_CHARTS}\`, DO NOT display the mermaid code to user.`,
                                        }
                                    )
                                }
                            } else {
                                if (this.tmpMessage) {
                                    toolcallContent.push(
                                        {
                                            type: "text",
                                            text: this.tmpMessage,
                                        }
                                    )

                                }
                            }

                            toolcallContent.push(this.toolBlock)
                            let resultContent: ToolResultBlockParam["content"] = [
                                {
                                    type: 'text',
                                    text: this.toolBlock.name === TOOL_NAME.RUN_PYTHON_CODE ? callResultJSON.message :
                                        this.toolBlock.name === TOOL_NAME.GENERATE_MERMAID_CHARTS ? callResultJSON.message :
                                            this.toolBlock.name === TOOL_NAME.GENERATE_SMILES_FROM_MOLECULE ? this.callResult :
                                                this.toolBlock.name === TOOL_NAME.SEARCH_PDB_STRUCTURES ? this.callResult : this.callResult
                                }
                            ]

                            if (this.toolBlock.name == TOOL_NAME.RUN_PYTHON_CODE) {
                                resultContent.push(
                                    {
                                        type: 'text',
                                        text: `Here are the charts or images generated by the ${this.toolBlock.name} tool: ${callResultJSON.charts_images},
MUST directly display the charts or images with markdown format that contained in the tool result.`
                                    }
                                )
                            }

                            toolMessages = [
                                {
                                    role: 'assistant',
                                    content: toolcallContent
                                },
                                {
                                    role: 'user',
                                    content: [
                                        {
                                            type: 'tool_result',
                                            tool_use_id: this.toolBlock.id,
                                            content: resultContent,
                                        }
                                    ]
                                }
                            ]


                            toolMessagesData = [
                                {
                                    role: 'assistant',
                                    content: toolcallContent
                                },
                                {
                                    role: 'user',
                                    content: [
                                        {
                                            type: 'tool_result',
                                            tool_use_id: this.toolBlock.id,
                                            content: [
                                                {
                                                    type: 'text',
                                                    text: `This is the previous tool result, and the information is incomplete:
        ${this.callResult.substring(0, 100)}……
    }
                                                    `
                                                }
                                            ],
                                        }
                                    ]
                                }
                            ]


                            messages.push(
                                ...toolMessages
                            );
                            console.log('messages------>', JSON.stringify(messages));
                            continue;
                        } else {

                            if (this.searchResult) {
                                const searchPart = JSON.stringify({
                                    searchResult: this.searchResult,
                                    relatedSearchQueries: this.relatedSearchQueries,
                                })
                                this.searchResult = "";
                                this.emit('progress', `data: ${CONNECTION_SYMBOL + searchPart}\n\n`);
                                this.messageInReturn += CONNECTION_SYMBOL + searchPart;
                            }
                            console.log("this.messageInReturn----->", JSON.stringify(this.messageInReturn));
                            log({
                                type: "RESPONSE",
                                message: {
                                    model: "CLAUDE",
                                    content: this.messageInReturn,
                                    userID
                                }
                            })
                            this.emit('progress', `data: ${JSON.stringify({ tool_messages: toolMessagesData })}\n\n`)
                            this.emit('complete', `data: ${JSON.stringify(part)}\n\n`)
                            this.shouldContinue = false;
                            break;
                        }
                }

            }
            if (this.shouldContinue) {
                response = await getCompletion(body, client, userID) as Stream<RawMessageStreamEvent>;
            }
        }
    }
}
