# SMILES 生成功能说明文档

## 1. 功能概述

本功能的核心是提供一个能够将多种化学标识符（如常用名、IUPAC名、CAS号等）转换为规范化SMILES (Simplified Molecular-Input Line-Entry System) 字符串的工具。SMILES是表示分子结构的线性符号，是各种化学绘图和建模软件的基础。

前端或上游服务可以通过调用相应的API端点，获取任一化学标识符对应的SMILES字符串，进而用于生成二维或三维的分子结构图。

## 2. 实现原理

整个功能实现为一个由AI驱动的工具，其调用链如下：

1.  **AI Agent (上游)**: 当需要处理化学式时，AI会根据优化后的工具描述，选择调用 `generate_smiles_from_molecule` 工具，并提供一个它认为最可靠的化学标识符（优先是CAS号）。

2.  **Node.js 中转层 (本项目)**:
    *   接收到工具调用请求，执行 [`lib/tools/generate-smiles-from-molecule.ts`](lib/tools/generate-smiles-from-molecule.ts) 中的逻辑。
    *   该逻辑会调用 [`lib/tools/run-python-code.ts`](lib/tools/run-python-code.ts) 工具。

3.  **Python 沙箱 (E2B 环境)**:
    *   `run-python-code` 工具会将一个动态生成的Python脚本发送到沙箱执行。
    *   该脚本首先通过 `pip` 动态安装 `rdkit-pypi` 和 `cirpy` 两个必要的化学信息学库。
    *   随后，脚本使用 `cirpy` 库尝试将输入的标识符解析为SMILES。`cirpy` 是NIST化学标识符解析服务的客户端，能力非常强大。
    *   如果 `cirpy` 解析失败，代码会回退，尝试直接将输入作为SMILES用 `rdkit` 解析。
    *   最终，脚本将解析得到的、包含立体化学信息的规范化SMILES字符串打印到标准输出。

4.  **结果返回**:
    *   执行结果（标准输出）返回给Node.js层，经过层层解析和处理，最终通过 [`/api/app/validate-smiles`](./route.ts) 接口返回给调用者。

## 3. 关键文件

-   **工具实现**:
    -   [`lib/tools/generate-smiles-from-molecule.ts`](lib/tools/generate-smiles-from-molecule.ts): 新增的核心工具，负责编排Python脚本并处理返回结果。
    -   [`lib/tools/run-python-code.ts`](lib/tools/run-python-code.ts): 被修改以支持动态安装额外依赖 (`extraDependencies`)，并通过返回 `stdout` 字段来更好地隔离执行结果和日志。
-   **工具注册与描述**:
    -   [`lib/tools/tool-info.ts`](lib/tools/tool-info.ts): 新增了 `GENERATE_SMILES_FROM_MOLECULE` 工具的定义，并为其编写了详尽的描述，以引导AI提供更精确的标识符。
    -   [`lib/tools/index.ts`](lib/tools/index.ts): 将新工具注册到系统的工具映射 (`toolsMap`) 和 `openAITools` 列表中。
-   **API 接口**:
    -   [`app/api/app/validate-smiles/route.ts`](./route.ts): 新增的API路由，提供给前端或外部服务调用。

## 4. API 端点说明

-   **URL**: `POST /api/app/validate-smiles`
-   **Content-Type**: `application/json`
-   **请求体 (Body)**:
    ```json
    {
      "molecule": "<chemical_identifier>"
    }
    ```
    -   `molecule`: `string`，必需。一个有效的化学标识符，推荐使用CAS号。
-   **成功响应 (200 OK)**:
    ```json
    {
      "status": "success",
      "data": "CC(=O)Oc1ccccc1C(=O)O"
    }
    ```
    -   `data` 字段包含纯净的SMILES字符串。
-   **失败响应**:
    ```json
    {
      "status": "fail",
      "message": "Invalid molecule"
    }
    ```

## 5. 如何测试

确保本地开发服务器正在运行。

### 使用 PowerShell

```powershell
Invoke-RestMethod -Uri http://localhost:3000/api/app/validate-smiles -Method Post -ContentType 'application/json' -Body '{"molecule": "aspirin"}'
```

### 使用 curl

```bash
curl -X POST http://localhost:3000/api/app/validate-smiles \
-H "Content-Type: application/json" \
-d '{"molecule": "aspirin"}'
```
