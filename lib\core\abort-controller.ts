export class AbortControllerContainer {
    public static controllers: AbortController[] = [];
    public static addController(ctrl: AbortController) {
        this.controllers.push(ctrl);
    }
    public static abort() {
        this.controllers.forEach(ctrl => {
            ctrl.abort();
        });
        this.controllers = [];
    }
}

export function createAbortController() {
    const ctrl = new AbortController();
    AbortControllerContainer.addController(ctrl);
    return ctrl;
}