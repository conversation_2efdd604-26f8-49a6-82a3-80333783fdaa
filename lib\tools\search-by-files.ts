import { Tool } from 'openai-function-calling-tools'
import { z } from 'zod';
import { toolInfoMap, TOOL_NAME } from './tool-info';
import { decryptFileLink } from '../core/file-link-encryption';
import { FILE_TYPE } from '../core/enum';

function createSearchByFiles() {

  const toolName = TOOL_NAME.SEARCH_BY_FILES;
  const fileSchema = z.object({
    fileId: z.string().describe(toolInfoMap.get(toolName)!.params.fileId),
    fileName: z.string().describe(toolInfoMap.get(toolName)!.params.fileName),
    fileType: z.union([z.literal(FILE_TYPE.TEXT), z.literal(FILE_TYPE.AUDIO)]).describe(toolInfoMap.get(toolName)!.params.fileType),
  })
  const paramsSchema = z.object({
    searchQuery: z.string().describe(toolInfoMap.get(toolName)!.params.searchQuery),
    files: z.array(fileSchema).describe(toolInfoMap.get(toolName)!.params.files),
  });

  type ParamsType = z.infer<typeof paramsSchema>
  type FileType = z.infer<typeof fileSchema>
  const execute = async ({ files, searchQuery }: ParamsType) => {
    console.log("files-----", files);
    function fileContentTemplate(fileName: string, content: string) {
      return `
        -----------------------------------------------------
        BEGINING of the contents of the file \`${fileName}\`:
        -----------------------------------------------------
        ${JSON.stringify(content)}
        -----------------------------------------------------
        END of the contents of the file\`${fileName}\`
        -----------------------------------------------------
      `;
    }

    async function getFileContent(file: FileType) {
      const { fileId, fileName } = file;
      console.log('search file:', fileName);
      const fileLink = decryptFileLink(fileId);
      const res = await fetch(
        `${process.env.RAG_ONE_URL}/api/search-by-url?url=${fileLink}&q=${searchQuery}`,
      );
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const content = await res.json();
      return {
        fileName,
        content,
      }
    }

    let contents = await Promise.all(files.map((file) => {
      return getFileContent(file);
    }));

    const res = contents.map((item) => {
      return fileContentTemplate(item.fileName, item.content);
    })

    return res.join("\n");

  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;

}

export { createSearchByFiles };