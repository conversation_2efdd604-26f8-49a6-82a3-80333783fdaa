import log from "@/lib/logs";
import { toolsMap } from "@/lib/tools";
import { TOOL_NAME } from "@/lib/tools/tool-info";
import { v4 as uuidv4 } from 'uuid';
import { TOOL_EXECUTION_MESSAGE } from "./constants";
import EventEmitter from "events";

export default async function callTool(
    toolName: TOOL_NAME,
    parsedArgs: any,
    userID: string,
    artifactEvent?: EventEmitter,
    historyMessages?: any[]
) {
    let callResult = "";
    const uuid = uuidv4();

    log({
        type: "TOOL_CALL",
        message: {
            type: 'tool_call',
            uuid,
            tool: toolName,
            parsedArgs,
            userID,
        },
    })


    try {
        switch (toolName) {

            case TOOL_NAME.REVERSE_GEOCODE:
                const { address } = await toolsMap.get(toolName)!(parsedArgs);
                callResult = address;
                break;

            case TOOL_NAME.SHOW_POIS_ON_MAP:
            case TOOL_NAME.CREATE_IMAGES:
                const { imageURL } = await toolsMap.get(toolName)!(parsedArgs)
                if (!imageURL) {
                    throw new Error(`tool's result is empty,MUST recall the '${toolName}' tool with new valid arguments`)
                }
                callResult = `
                    ## Here is the image url of tool's execution result: \`${imageURL}\`
                    ## The image type may be jpg, jpeg, png, svg,or map image start with 'https://api.mapbox.com/'
                    ## MUST directly display the image with Markdown format,for example:
                        - **SVG**:  ![image](https://example.com/image.svg)
                        - **PNG**:  ![image](https://example.com/image.png)
                        - **JPG**:  ![image](https://example.com/image.jpg)
                        - **JPEG**:  ![image](https://example.com/image.jpeg)
                        - **Map Image**: ![image](https://api.mapbox.com/xxxxxxxx)
                `;
                break;


            case TOOL_NAME.GENERATE_ARTIFACTS:
                callResult = await toolsMap.get(toolName)!({
                    ...parsedArgs,
                    artifactEvent,
                    historyMessages
                })
                break;

            case TOOL_NAME.RUN_PYTHON_CODE:
            case TOOL_NAME.UPLOAD_FILE_TO_SANDBOX:
                // TOOL need the userID to create a sandbox
                callResult = await toolsMap.get(toolName)!({
                    ...parsedArgs,
                    userID,
                });
                break;
            default:
                // Just get the results
                callResult = await toolsMap.get(toolName)!(parsedArgs);
        }

        return {
            callResult,
            uuid
        }
    } catch (error) {
        log({
            type: 'ERROR',
            message: {
                userID,
                error: error,
            },
        });
        return {
            callResult: TOOL_EXECUTION_MESSAGE,
            uuid
        }
    } finally {
        console.log("callTool finally")
    }

}