import { getSystemMessage } from "./sys-message";
import { ChatCompletionCreateParamsBase } from "openai/resources/chat/completions";

import OpenAI from "openai";
import { omit } from "lodash";
import { generateInstructionBasedOnPrompt } from "../../common/prompt-instruction";
import { replaceCelhiveLink } from "@/lib/utils";

export const getCompletion = async (
    body: ChatCompletionCreateParamsBase & { deepThinking?: boolean },
    client: OpenAI
) => {

    const newBody = omit(body, ['deepThinking']);

    let newMessages: any[] = body.messages;
    const messages = getSystemMessage(body.model, newMessages);
    if (body.model.startsWith("deepseek")) {
        newMessages = body.messages.map(message => ({
            ...message,
            content: typeof message.content === 'string' && message.role === 'assistant' && message.content.includes('<juchats-thinking>')
                ? message.content.replace(/<juchats-thinking>[\s\S]*?<\/juchats-thinking>/g, '')
                : message.content
        }));
    } else {
        if (messages && messages.length > 0) {
            const lastMessage = messages[messages.length - 1];

            if (lastMessage.role === 'user' && typeof lastMessage.content === 'string') {
                lastMessage.content = replaceCelhiveLink(lastMessage.content);
                lastMessage.content = generateInstructionBasedOnPrompt(lastMessage.content, newBody.model);
            }
        }

    }



    const params = {
        ...newBody,
        top_p: parseFloat('0.9'),
        messages,
    };
    console.log('openai params---->', JSON.stringify(params))

    return await client.chat.completions.create(params);
}
