import { Tool } from 'openai-function-calling-tools';
import { z } from 'zod';
import path from 'path';
import fs from 'fs';
import { createSandBoxManager } from '../core/sand-box';
import { v4 as uuidv4 } from 'uuid';
import { toolInfoMap, TOOL_NAME } from './tool-info';
import { uploadToOSS } from '../core/upload-to-oss';
import { decodeFromUnicode } from '../utils';

function createRunPythonCode() {
  const toolName = TOOL_NAME.RUN_PYTHON_CODE;
  const paramsSchema = z.object({
    code: z.string().describe(toolInfoMap.get(toolName)!.params.code),
    userID: z.string().optional().describe('User ID'),
    extraDependencies: z.array(z.string()).optional().describe('Extra dependencies to install'),
  });

  const execute = async ({ code, userID, extraDependencies }: z.infer<typeof paramsSchema>) => {
    let fullCode = decodeFromUnicode(code);
    if (extraDependencies && extraDependencies.length > 0) {
        const installCommand = `!pip install ${extraDependencies.join(' ')}`;
        fullCode = `${installCommand}\n${fullCode}`;
    }

    const sandBoxManager = await createSandBoxManager(userID!);
    const { results, error, logs: { stdout } } = await sandBoxManager.sandBox!.runCode(fullCode, { timeoutMs: 300_000, requestTimeoutMs: 300_000 });
    let uploadedResult: string[] = [];
    if (error) {
      return `There was an error during execution: ${error.name}: ${error.value}.\n ${error.traceback}`
    }

    let message = `These are results of the execution:\n`
    if (results.length > 0) {
      console.log('python execution results:', results);

      let counter = 1;
      for (const result of results) {
        const { jpeg, png, svg, isMainResult, text } = result;
        message += `Result ${counter++}:\n`;
        if (isMainResult) {
          message += `## Main result: ${text}\n`
        } else {
          message += `## Display data: ${text}\n`
        }

        if (jpeg) {
          const uploadedURL = await handleBase64(jpeg, 'jpg');
          uploadedResult.push(uploadedURL);
        }

        if (png) {
          const uploadedURL = await handleBase64(png, 'png');
          uploadedResult.push(uploadedURL);
        }

        if (svg) {
          const filePath = `/tmp/${uuidv4().replaceAll("-", '')}.svg`;
          fs.writeFileSync(filePath, svg);
          const fileLink = await uploadToOSS({ filePath });
          uploadedResult.push(fileLink);
        }

        if (uploadedResult.length) {
          message += `
## URLs of Images And Charts: \`${uploadedResult.join(', ')}\` 
          `
        }
      }
    }

    if (stdout && stdout.length > 0) {
      message += `## Logs result:
\`\`\`
${JSON.stringify(stdout)}
\`\`\`
      `
    }

    message += `
# MUST directly display the charts or images: "${uploadedResult.join(', ')}" in markdown image syntax format,for example: ![image](https://example.com/image.jpg)
    `

    // await sandBoxManager.close();
    return JSON.stringify({
      message,
      stdout: stdout,
      charts_images: uploadedResult,
    });

  }
  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;

};


async function handleBase64(base64String: string, ext: 'png' | 'jpg') {
  const buffer = Buffer.from(base64String, 'base64');
  const fileName = `${uuidv4().replaceAll("-", '')}.${ext}`;
  const filePath = path.join("/tmp", fileName);
  fs.writeFileSync(filePath, new Uint8Array(buffer));
  const fileLink = await uploadToOSS({ filePath, remotePath: 'codeinterpreter' });
  return fileLink;
}


export { createRunPythonCode };
