import { Tool } from 'openai-function-calling-tools'
import { z } from 'zod';
import { toolInfoMap, TOOL_NAME } from './tool-info';
import { decryptFileLink } from '../core/file-link-encryption';
import { FILE_TYPE } from '../core/enum';

function createSummarizeFile() {

  const toolName = TOOL_NAME.SUMMARIZE_FILE;
  const paramsSchema = z.object({
    fileId: z.string().describe(toolInfoMap.get(toolName)!.params.fileId),
    fileType: z.union([z.literal(FILE_TYPE.TEXT), z.literal(FILE_TYPE.AUDIO)])
  });

  const execute = async ({ fileId, fileType }: z.infer<typeof paramsSchema>) => {

    if (fileType !== FILE_TYPE.TEXT && fileType !== FILE_TYPE.AUDIO)
      throw new Error('File MUST be a Text File or Audio File.')

    const URL = decryptFileLink(fileId);
    const res = await fetch(
      `${process.env.RAG_ONE_URL}/api/summarize?url=${encodeURI(decodeURIComponent(URL))}`
    );

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    const data = await res.json();

    return JSON.stringify(data);


  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;

}

export { createSummarizeFile };