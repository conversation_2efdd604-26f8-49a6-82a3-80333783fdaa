import { NextRequest } from 'next/server'
import log from '@/lib/logs'
import { HttpStatusCode } from 'axios';
import { authToken } from './auth';

export default async function authApp(request: NextRequest) {

    const Jtoken = request.headers.get('Jtoken');

    if (!Jtoken)
        throw new Error(`${HttpStatusCode.Unauthorized}`)

    const res = await authToken(Jtoken);
    if (res.code !== HttpStatusCode.Ok) {
        throw new Error(res.msg)
    }

    log({
        type: 'HTTP',
        message: {
            type: 'REQUEST-APP',
            method: request.method,
            url: request.url,
        },
    });

    return res.data;

}
