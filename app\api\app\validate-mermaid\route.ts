import { fail, success } from '@/lib/core/response'
import { decodeFromUnicode } from '@/lib/utils';
import mermaid from 'mermaid';


export async function POST(req: Request) {
  //expire in seconds
  const {
    mermaidCode
  }: { mermaidCode: string; } =
    await req.json()

  try {
    const decodedMermaidCode = decodeFromUnicode(mermaidCode);
    const parsedMermaidCodeResult = await mermaid.parse(decodedMermaidCode);

    if (parsedMermaidCodeResult)
      return success(decodedMermaidCode);
  } catch (error) {
    return fail('Invalid mermaid code');
  }

}

