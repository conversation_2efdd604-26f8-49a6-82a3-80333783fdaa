import { Tool } from 'openai-function-calling-tools'
import { z } from 'zod';
import { toolInfoMap, TOOL_NAME } from './tool-info';

function createSearchByUrl() {
  const toolName = TOOL_NAME.SEARCH_BY_URL;
  const paramsSchema = z.object({
    searchQuery: z.string().describe(toolInfoMap.get(toolName)!.params.searchQuery),
    URL: z.string().describe(toolInfoMap.get(toolName)!.params.URL),
  });

  const execute = async ({ URL, searchQuery }: z.infer<typeof paramsSchema>) => {

    const res = await fetch(
      `${process.env.RAG_URL}/api/search-by-url?url=${URL}&q=${searchQuery}`
    );

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    const data = await res.json();

    return JSON.stringify(data);


  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;

}

export { createSearchByUrl };