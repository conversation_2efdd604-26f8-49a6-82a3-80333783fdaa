import { FragmentSchema } from '@/lib/artifacts/schema'
import { ExecutionResultInterpreter, ExecutionResultWeb } from '@/lib/artifacts/types'
import { success } from '@/lib/core/response'
import { Sandbox } from '@e2b/code-interpreter'
import { deploy_nextjs } from './deploy'
import redis from '@/lib/utils/redis'
import dayjs from 'dayjs'

const sandboxTimeout = 3 * 60 * 1000 // 3 minutes in ms


export async function POST(req: Request) {
  const {
    fragment,
  }: { fragment: FragmentSchema; } =
    await req.json()

  const userID = req.headers.get('userID')!;

  // Create a interpreter or a sandbox
  const sbx = await Sandbox.create(fragment.template, {
    timeoutMs: sandboxTimeout,
    metadata: { template: fragment.template, id: `artifact-${userID}` },
  })

  // Install packages
  if (fragment.has_additional_dependencies) {
    if (fragment.template === process.env.E2B_NEXTJS_TEMPLATE) {
      await sbx.commands.run(fragment.install_dependencies_command, {
        cwd: '/home/<USER>/nextjs-app'
      })
    } else {
      await sbx.commands.run(fragment.install_dependencies_command)
    }
    console.log(
      `Installed dependencies: ${fragment.additional_dependencies.join(', ')} in sandbox ${sbx.sandboxId}`,
    )
  }

  // Copy code to fs
  if (fragment.code && Array.isArray(fragment.code)) {
    fragment.code.forEach(async (file) => {
      await sbx.files.write(file.file_path, file.file_content)
      console.log(`Copied file to ${file.file_path} in ${sbx.sandboxId}`)
    })
  } else {
    await sbx.files.write(fragment.file_path, fragment.code)
    console.log(`Copied file to ${fragment.file_path} in ${sbx.sandboxId}`)
  }

  let result: ExecutionResultInterpreter | ExecutionResultWeb;

  console.log('fragment---->', fragment.template);
  // Execute code or return a URL to the running sandbox
  if (fragment.template === process.env.E2B_CODE_INTERPRETER_TEMPLATE) {
    const { logs, error, results } = await sbx.runCode(fragment.code || '')
    result = {
      sbxId: sbx?.sandboxId,
      template: fragment.template,
      expire: dayjs().add(sandboxTimeout, 'ms').format('YYYY-MM-DD HH:mm:ss'),
      stdout: logs.stdout,
      stderr: logs.stderr,
      runtimeError: error,
      cellResults: results,
    } as ExecutionResultInterpreter;

  } else {
    // if (fragment.template === 'nextjs-developer-vercel-test') {
    //   await redis.set(sbx.sandboxId, 'deploying')
    //   deploy_nextjs(sbx.sandboxId).then(async (deploy: { name: string; url: string; }) => {
    //     await redis.set(sbx.sandboxId, JSON.stringify(deploy))
    //   }).catch(async (error) => {
    //     console.error(`Deployment failed for sandbox ${sbx.sandboxId}: ${error}`);
    //     await redis.del(sbx.sandboxId);
    //   });
    // }

    const expire = dayjs().add(sandboxTimeout, 'ms').format('YYYY-MM-DD HH:mm:ss')
    console.log("expire:", expire)

    result = {
      sbxId: sbx?.sandboxId,
      template: fragment.template,
      expire: expire,
      url: `https://${sbx?.getHost(fragment.port || 80)}`,
    } as ExecutionResultWeb;
  }

  return success(result);

}

