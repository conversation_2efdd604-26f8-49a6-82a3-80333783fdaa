import { Tool } from 'openai-function-calling-tools'
import { z } from 'zod';
import { toolInfoMap, TOOL_NAME } from './tool-info';
import { MERMAID_SUCCESS_MESSAGE, TOOL_EXECUTION_MESSAGE } from '@/app/api/chat/common/constants';
import mermaid from 'mermaid';
import { decodeFromUnicode } from '../utils';


function createGenerateMermaidCharts() {

  const toolName = TOOL_NAME.GENERATE_MERMAID_CHARTS;
  const paramsSchema = z.object({
    mermaidCode: z.string().describe(toolInfoMap.get(toolName)!.params.mermaidCode),
  });

  const execute = async ({ mermaidCode }: z.infer<typeof paramsSchema>) => {

    try {

      console.log('mermaidCode---->', mermaidCode)
      const decodedMermaidCode = decodeFromUnicode(mermaidCode);
      console.log('decodedMermaidCode---->', decodedMermaidCode)

      const parsedMermaidCodeResult = await mermaid.parse(decodedMermaidCode);
      console.log('parsedMermaidCodeResult---->', parsedMermaidCodeResult)

      if (parsedMermaidCodeResult)
        return JSON.stringify({
          message: MERMAID_SUCCESS_MESSAGE,
          mermaidCode: decodedMermaidCode,
        });

      return JSON.stringify({
        message: TOOL_EXECUTION_MESSAGE,
      });
    } catch (error) {
      console.log('error---->', error)
      return JSON.stringify({
        message: TOOL_EXECUTION_MESSAGE,
      });
    }
  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;

}

export { createGenerateMermaidCharts };
