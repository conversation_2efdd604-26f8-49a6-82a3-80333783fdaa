import { decryptFileLink } from "@/lib/core/file-link-encryption";
import { ParseRes } from "../../model/parseModel";
import { fail, success } from "@/lib/core/response";


export async function POST(req: Request) {


    const body = await req.json();
    const { fileId } = body;
    const fileLink = encodeURIComponent(decryptFileLink(fileId));
    console.log('fileLink----->', fileLink)
    console.log('process.env.RAG_URL----->', process.env.RAG_URL)
    const res = await fetch(`${process.env.RAG_URL}/api/file-parser-and-embedding?url=${fileLink}`);

    if (res.ok) {
        const json: ParseRes = await res.json();
        if (json.code == 0) {
            return success<null>(null)
        }
        console.log('error parse----', json)
        return fail('parse fetch failed!')
    }
    console.log('error parse----', res);
    return fail('parse failed!');


}

