import { Tool } from 'openai-function-calling-tools';
import { z } from 'zod';
import { toolInfoMap, TOOL_NAME } from './tool-info';
import { SEARCH_ERROR_MESSAGE } from '@/app/api/chat/common/constants';

// 定义 NewData 类型
interface NewData {
  code: number;
  message: string;
  data: {
    organic: Item[];
  };
}

// 定义 Item 类型
interface Item {
  title: string;
  link: string;
  snippet: string;
  position: number;
  date?: string;
  contexts?: Context[];
}

// 定义 Context 类型
interface Context {
  idx: number;
  text: string;
}



function createSearchOnline() {
  const toolName = TOOL_NAME.SEARCH_ONLINE;
  const paramsSchema = z.object({
    searchQueries: z.array(z.string())
      .length(3, "Must provide exactly 3 search queries")
      .describe(toolInfoMap.get(toolName)!.params.query),
  });

  const execute = async ({ searchQueries }: z.infer<typeof paramsSchema>) => {

    try {
      return await browsing(searchQueries);
    } catch (error: any) {
      console.error('Search error details:', {
        error,
        errorName: error.name,
        errorMessage: error.message,
        timestamp: new Date().toISOString()
      });
      return SEARCH_ERROR_MESSAGE
    }

  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;
}

// 格式化数据的函数
function formatData(newData: NewData) {
  const organic = newData.data.organic;
  const formattedData = organic.map(item => {
    const { title, link, snippet, position, date, contexts } = item;
    const formattedItem: Partial<Item> = {
      title,
      link,
      snippet,
      position
    };
    if (date) {
      formattedItem.date = date;
    }
    formattedItem.contexts = contexts || [];
    return formattedItem;
  });
  return formattedData;
}

export async function browsing(searchQueries: string[]) {
  const excludeLinks = ['*.csdn.net', '*.cnblogs.com']
  console.log("search base url", `${process.env.RAG_URL}`)

  try {
    console.log('Starting multiple search requests...');
    const startTime = Date.now();

    // 并行执行所有搜索查询
    const searchPromises = searchQueries.map(async (searchQuery, index) => {
      console.log(`Starting search ${index + 1}/${searchQueries.length}: ${searchQuery}`);
      const queryStartTime = Date.now();

      try {
        const res = await fetch(
          `${process.env.RAG_URL}/api/search?q=${encodeURIComponent(searchQuery)} ${excludeLinks.map(link => `-site:${link}`).join(' ')}`,
          {
            signal: AbortSignal.timeout(180000)
          }
        );

        console.log(`Search ${index + 1} completed in ${Date.now() - queryStartTime}ms`);

        if (!res.ok) {
          console.error(`Search ${index + 1} failed with status: ${res.status}`);
          return null;
        }

        const data: NewData = await res.json();
        const formattedData = formatData(data);

        // 为每个搜索结果添加查询标识
        return {
          query: searchQuery,
          results: formattedData
        };
      } catch (error: any) {
        console.error(`Search ${index + 1} error:`, {
          query: searchQuery,
          error: error.message,
          timestamp: new Date().toISOString()
        });
        return null;
      }
    });

    // 等待所有搜索完成
    const searchResults = await Promise.all(searchPromises);

    console.log(`All searches completed in ${Date.now() - startTime}ms`);

    // 过滤掉失败的搜索，合并所有结果
    const validResults = searchResults.filter(result => result !== null);

    if (validResults.length === 0) {
      return SEARCH_ERROR_MESSAGE;
    }

    // 如果只有一个查询，返回原来的格式以保持兼容性
    if (searchQueries.length === 1 && validResults.length === 1) {
      return JSON.stringify(validResults[0].results);
    }

    // 合并所有查询结果并重新排序position
    const allResults = validResults.flatMap(result => result.results);
    allResults.forEach((result, index) => {
      result.position = index + 1;
    });
    return JSON.stringify(allResults);

  } catch (error: any) {
    console.error('Search error details:', {
      error,
      errorName: error.name,
      errorMessage: error.message,
      timestamp: new Date().toISOString()
    });
    return SEARCH_ERROR_MESSAGE;
  }
}

export { createSearchOnline };