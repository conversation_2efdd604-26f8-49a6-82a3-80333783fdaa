import fs from "node:fs";
import axios from "axios";
import FormData from "form-data";
import path from "path";
import { v4 as uuidv4 } from 'uuid';
import log from "../logs";
export async function uploadToOSS(
    {
        filePath,
        fileLink,
        remotePath = 'file'
    }: {
        filePath?: string,
        fileLink?: string,
        remotePath?: string,
    }
) {

    const formData = new FormData();

    let fileName = ''
    if (filePath) {
        formData.append('file', fs.createReadStream(filePath));
        fileName = path.basename(filePath);
    }
    if (fileLink) {
        const parsedUrl = new URL(fileLink);
        const pathname = parsedUrl.pathname;
        const extname = path.extname(pathname);

        formData.append('fileLink', fileLink);
        fileName = uuidv4().replaceAll("-", '') + extname;
    }

    formData.append('fileName', fileName);
    formData.append('remotePath', remotePath);

    try {
        await axios.post('https://upload-to-r2.openaidev.workers.dev', formData, {
            headers: {
                ...formData.getHeaders(),
                'Authorization': `Bearer ${process.env.R2_KEY}`,
            },
        });
        
        return `https://hermosssvip.herm.tools/${remotePath}/${fileName}`;
    } catch (error: any) {
        log({
            type: 'ERROR',
            message: {
                error: error,
            },
        });
    }
    return '';
}
