import { generateAnthropicTools, generateOpenAITools } from '@/lib/tools';
import { getStreamResponse } from './core/stream-response';
import { TOOL_NAME } from '@/lib/tools/tool-info';


export async function POST(req: Request) {
    console.log("process.env.ENV", process.env.ENV)
    const body = await req.json();
    const authHeader = req.headers.get('Authorization')!;
    const userID = req.headers.get('userID')!;
    const apiKey = authHeader.split('Bearer ')[1];
    console.log("apiKey", apiKey);
    console.log("baseURL", process.env.ANTHROPIC_BASE_URL);
    const openaiTools = generateOpenAITools(body.tools || []);
    let tools = generateAnthropicTools(openaiTools);

    delete body.top_p;

    if (
        !body.model.startsWith('claude-3-5-sonnet') &&
        !body.model.startsWith('claude-3-7-sonnet') &&
        !body.model.startsWith('claude-sonnet-4') &&
        !body.model.startsWith('claude-opus-4')
    ) {
        tools = tools.filter(tool => tool.name !== TOOL_NAME.GENERATE_ARTIFACTS);
    }

    const response = await getStreamResponse(
        {
            max_tokens: 8192,
            tool_choice: { type: 'auto' },
            model: 'claude-3-5-sonnet-20241022',
            ...body,
            temperature: 0.8,
            stream: true,
            tools,
        },
        userID,
        apiKey
    );
    return response;

}
