import { Tool } from 'openai-function-calling-tools'
import { z } from 'zod';
import { toolInfoMap, TOOL_NAME } from './tool-info';

function createSummarizeUrl() {

  const toolName = TOOL_NAME.SUMMARIZE_URL;
  const paramsSchema = z.object({
    URL: z.string().describe(toolInfoMap.get(toolName)!.params.URL),
  });

  const execute = async ({ URL }: z.infer<typeof paramsSchema>) => {

    let data: any = {};

    data = await getNormalSummarizeInfo(URL);

    return JSON.stringify(data);

  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;

}

export { createSummarizeUrl };


async function getNormalSummarizeInfo(url: string) {
  const res = await fetch(
    `${process.env.RAG_ONE_URL}/api/summarize?url=${url}`
  );

  if (!res.ok) {
    throw new Error(`HTTP error! status: ${res.status}`);
  }

  return await res.json();
}