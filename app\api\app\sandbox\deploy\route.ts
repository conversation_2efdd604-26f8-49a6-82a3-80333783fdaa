import { success } from '@/lib/core/response'
import { deploy_nextjs } from '../deploy'
import redis from '@/lib/utils/redis';
import dayjs from 'dayjs';


export async function POST(req: Request) {
  //expire in seconds
  const {
    sandboxId,
    template,
    expire = 0
  }: { sandboxId: string; template: string; expire: number; } =
    await req.json()
  let deployResult: { name?: string; url?: string; expire?: string; } = {}
  if (template === process.env.E2B_NEXTJS_TEMPLATE) {
    let res = await redis.get(sandboxId);
    if (!res) {
      console.log('deploying...')
      const startTime = new Date();
      deployResult = await deploy_nextjs(sandboxId);
      const endTime = new Date();
      console.log(`Deploy took ${(endTime.getTime() - startTime.getTime()) / 1000} seconds`);
    } else {
      while (res === 'deploying') {
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for 1 second before checking again
        res = await redis.get(sandboxId);
      }
      if (res) {
        deployResult = JSON.parse(res);
        await redis.del(sandboxId);
      }
    }
    if (expire > 0) {
      deployResult.expire = dayjs().add(expire, 's').format('YYYY-MM-DD HH:mm:ss')
    } else {
      deployResult.expire = ""
    }



  }
  return success(deployResult);

}

