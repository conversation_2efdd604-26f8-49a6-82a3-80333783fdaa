import { NextRequest } from 'next/server'
import log from '@/lib/logs'

export default async function authChat(request: NextRequest) {
    const userID = request.headers.get('userID');
    const Authorization = request.headers.get('Authorization');
    if (!userID)
        throw new Error('Authentication failed！Permission denied!')
    if (!Authorization)
        throw new Error('Authentication failed！');
    request.json().then((body) => {
        log({
            type: 'HTTP',
            message: {
                type: 'REQUEST-CHAT',
                userID: userID,
                method: request.method,
                url: request.url,
                body,
            },
        }, false);
    });
}
