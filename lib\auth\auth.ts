import { HttpStatusCode } from "axios";
import { ResponseRes } from "../types/responseModel";
import log from "../logs";

export async function authToken(token: string) {

    console.log('BACK_END_URL---->', process.env.BACK_END_URL);

    const url = `${process.env.BACK_END_URL}/gw/chatweb/user/cTk`
    const res = await fetch(url, {
        method: 'POST',
        headers: {
            Jtoken: token
        }
    });
    if (res.status !== HttpStatusCode.Ok) {
        throw new Error(`res.status`)
    }
    const json: ResponseRes<null> = await res.json();
    log({
        type: 'COMMON',
        message: {
            type: 'Auth:',
            content: JSON.stringify(json),
        }
    });
    return json;

}