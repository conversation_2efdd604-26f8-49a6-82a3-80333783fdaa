import { NextResponse, NextRequest, userAgent } from 'next/server'
import log from '@/lib/logs'
import authChat from './lib/auth/auth-chat';
import authApp from './lib/auth/auth-app';
import { HttpStatusCode } from 'axios';
import { fail } from './lib/core/response';

export const config = {
    matcher: ['/v1/:path*'],
}

const cors = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

const whiteListPath = [
    '/v1/app/file/getLink',
    '/v1/app/tools',
    '/v1/app/validate-mermaid'
]

export async function middleware(request: NextRequest) {
    try {
        const url = new URL(request.url);
        const pathName = url.pathname;
        const isPreflight = request.method === 'OPTIONS'

        if (isPreflight) {
            const preflightHeaders = {
                ...cors
            }
            return NextResponse.json({}, { headers: preflightHeaders })
        }

        try {
            if (pathName.startsWith('/v1/chat/')) {
                await authChat(request);
            }
            if (
                pathName.startsWith('/v1/app/') &&
                !whiteListPath.includes(pathName)
            ) {
                const userID = await authApp(request);
                if (userID) {
                    return NextResponse.next({ headers: { userID } });
                }
            }
        } catch (error) {
            log({
                type: 'ERROR',
                message: {
                    error: error,
                    method: request.method
                },
            });
            return fail('Unauthorized', HttpStatusCode.Unauthorized);
        }
        return NextResponse.next();
    } catch (error: any) {
        log({
            type: 'ERROR',
            message: {
                error: error,
                method: request.method,
                url: request.url
            },
        });
        return fail();
    }

}
