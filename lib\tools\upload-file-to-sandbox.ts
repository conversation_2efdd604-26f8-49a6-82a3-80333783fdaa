import { createSandBoxManager } from '@/lib/core/sand-box';
import { Tool } from 'openai-function-calling-tools';
import { z } from 'zod';
import axios from 'axios';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { toolInfoMap, TOOL_NAME } from './tool-info';
import log from '../logs';
import { FILE_TYPE } from '../core/enum';
import { decryptFileLink } from '../core/file-link-encryption';

function createUploadFileToSandbox() {

  const toolName = TOOL_NAME.UPLOAD_FILE_TO_SANDBOX;
  const paramsSchema = z.object({
    fileId: z.string().describe(toolInfoMap.get(toolName)!.params.fileId),
    fileType: z.literal(FILE_TYPE.DATA).describe(toolInfoMap.get(toolName)!.params.fileType),
    userID: z.string().optional().describe('User ID'),
  });

  const execute = async ({ fileId, fileType, userID }: z.infer<typeof paramsSchema>) => {

    if (fileType !== FILE_TYPE.DATA)
      throw new Error('File MUST be a DATA File.')

    try {
      const fileLink = decryptFileLink(fileId);

      console.log('E2B_API_KEY-----', process.env.E2B_API_KEY)
      const sandBoxManager = await createSandBoxManager(userID!);
      const { localFilePath, fileBuffer, fileName } = await downloadFile(fileLink);
      fs.writeFileSync(localFilePath, fileBuffer);
      // @ts-ignore: Buffer type mismatch
      let remoteRes = await sandBoxManager.sandBox?.files.write(localFilePath, new Uint8Array(fileBuffer));

      return JSON.stringify({
        fileRemotePathFromSandbox: remoteRes?.path,
      }) || 'Upload Failed,Please try again.';

    } catch (error) {
      console.log('upload file to sandbox error---->', error)
      return 'Upload Failed,Please try again.'
    }
  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;
}

async function downloadFile(fileLink: string) {
  // 使用 axios 获取文件内容
  const response = await axios.get(fileLink, { responseType: 'arraybuffer' });

  // 将响应数据转换为 Buffer
  const fileBuffer = Buffer.from(response.data, 'binary');

  // uuid + _原文件名：
  let fileName = path.basename(fileLink);

  fileName = `${uuidv4().replaceAll("-", '')}_${fileName}`;

  // 构建本地文件存储路径
  const localFilePath = path.join("/tmp", fileName);
  // 将文件写入本地文件系统
  fs.writeFileSync(localFilePath, fileBuffer);

  log({
    type: "COMMON",
    message: {
      type: 'Download-OSS-File',
      content: 'File has been downloaded and saved',
      localFilePath,
    }

  })
  return { localFilePath, fileBuffer, fileName }

}


export { createUploadFileToSandbox };