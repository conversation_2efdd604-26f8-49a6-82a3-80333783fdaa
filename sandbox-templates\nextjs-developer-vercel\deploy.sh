#!/bin/bash

cd /home/<USER>/nextjs-app

# 生成一个基于当前时间戳的唯一项目名称
project_name="juchats-artifacts-$(date +%s)"

# 使用 Vercel CLI 链接项目
vercel link --yes --project "$project_name" --token ************************

# 检查上一个命令是否成功执行
if [ $? -eq 0 ]; then
	echo "项目链接成功，开始部署..."

	# 部署项目到生产环境
	vercel --prod --yes --token ************************

	# 检查部署命令是否成功执行
	if [ $? -eq 0 ]; then
		echo "部署成功"
	else
		echo "部署失败"
		exit 1
	fi
else
	echo "项目链接失败"
	exit 1
fi

# 将项目名称输出到 juchats-artifacts.txt 文件
echo "$project_name" | sudo tee juchats-artifacts.txt >/dev/null
