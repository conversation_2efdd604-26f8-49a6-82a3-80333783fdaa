import { Tool } from 'openai-function-calling-tools'
import { z } from 'zod';
import { toolInfoMap, TOOL_NAME } from './tool-info';
import { FunctionDefinition } from 'openai/resources';
import { TOOL_EXECUTION_MESSAGE } from '@/app/api/chat/common/constants';
import { createRunPythonCode } from '@/lib/tools/run-python-code';

function createGenerateSmilesFromMolecule() {

  const toolName = TOOL_NAME.GENERATE_SMILES_FROM_MOLECULE;
  const paramsSchema = z.object({
    molecule: z.string().describe(toolInfoMap.get(toolName)!.params.molecule),
  });

  const execute = async ({ molecule }: z.infer<typeof paramsSchema>) => {

    try {
      const pythonCode = `
import cirpy
from rdkit import Chem
import sys
import json

molecule_str = '${molecule}'
mol = None
success = False
result = {}

# List of possible identifiers to try
identifiers_to_try = [molecule_str]

# If the input looks like a common name, try some variations
if not any(char in molecule_str for char in ['=', '#', '(', ')', '[', ']']):
    # Try adding common chemical name variations
    variations = [
        molecule_str.lower(),
        molecule_str.upper(),
        molecule_str.capitalize()
    ]
    identifiers_to_try.extend(variations)

# Try to resolve each identifier
for identifier in identifiers_to_try:
    if success:
        break
    
    # Try CIRpy resolution
    try:
        smiles = cirpy.resolve(identifier, 'smiles')
        if smiles:
            test_mol = Chem.MolFromSmiles(smiles)
            if test_mol is not None:
                mol = test_mol
                success = True
                result['resolved_name'] = identifier
                result['method'] = 'cirpy'
                break
    except Exception:
        pass
    
    # Try direct SMILES interpretation
    if not success:
        try:
            test_mol = Chem.MolFromSmiles(identifier)
            if test_mol is not None:
                mol = test_mol
                success = True
                result['resolved_name'] = identifier
                result['method'] = 'direct_smiles'
                break
        except Exception:
            pass

# Output result
if success and mol is not None:
    canonical_smiles = Chem.MolToSmiles(mol)
    result['smiles'] = canonical_smiles
    result['success'] = True
    print(json.dumps(result))
else:
    result['success'] = False
    result['error'] = f"Could not resolve '{molecule_str}' to a valid molecule."
    print(json.dumps(result))
`;
      const runPythonCode = (createRunPythonCode() as [Function, FunctionDefinition])[0];
      const rawResult = await runPythonCode({ code: pythonCode, userID: 'smiles-generator', extraDependencies: ['rdkit-pypi', 'cirpy'] });
      const parsedResult = JSON.parse(rawResult);
      const outputLine = parsedResult.stdout[parsedResult.stdout.length - 1].trim();
      
      try {
        const pythonResult = JSON.parse(outputLine);
        
        if (pythonResult.success) {
          return JSON.stringify({
            message: `Successfully converted ${molecule} to SMILES.`,
            smiles: pythonResult.smiles,
            resolved_name: pythonResult.resolved_name,
            method: pythonResult.method
          });
        } else {
          return JSON.stringify({
            message: pythonResult.error,
          });
        }
      } catch (parseError) {
        // Fallback to old behavior if JSON parsing fails
        return JSON.stringify({
          message: `Successfully converted ${molecule} to SMILES.`,
          smiles: outputLine,
        });
      }

    } catch (error) {
      console.log('error---->', error)
      return JSON.stringify({
        message: TOOL_EXECUTION_MESSAGE,
      });
    }
  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;

}

export { createGenerateSmilesFromMolecule };
