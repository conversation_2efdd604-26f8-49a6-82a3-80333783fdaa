import CryptoJS from 'crypto-js';

const secretKey = process.env.FILE_SECRET_KEY!;

/**
 * Encrypts a file link using AES encryption.
 * @param fileLink - The file link to be encrypted.
 * @returns The encrypted file link.
 */
export function encryptFileLink(fileLink: string) {
    return CryptoJS.AES.encrypt(fileLink, secretKey).toString();
}

/**
 * Decrypts an encrypted file link using AES decryption.
 * @param encryptedFileLink - The encrypted file link to be decrypted.
 * @returns The decrypted file link.
 */
export function decryptFileLink(encryptedFileLink: string) {
    const bytes = CryptoJS.AES.decrypt(encryptedFileLink, secretKey);
    const decryptedFileLink = bytes.toString(CryptoJS.enc.Utf8);
    return decryptedFileLink.replace("https://oss.hermchats.com", "https://hermosssvip.herm.tools");
}
