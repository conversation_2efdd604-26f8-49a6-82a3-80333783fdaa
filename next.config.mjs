/** @type {import('next').NextConfig} */
import { JSD<PERSON> } from "jsdom";

const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
global.document = dom.window.document;
global.window = dom.window;
global.navigator = dom.window.navigator;

const nextConfig = {
	async rewrites() {
		return [
			{
				source: "/v1/:path*",
				destination: "/api/:path*",
			}
		];
	},

	async headers() {
		// * add cors
		return [
			{
				source: "/:path*",
				headers: [
					{ key: "Access-Control-Allow-Credentials", value: "true" },
					{ key: "Access-Control-Allow-Origin", value: "*" },
					{
						key: "Access-Control-Allow-Methods",
						value: "GET,DELETE,PATCH,POST,PUT",
					},
					{
						key: "Access-Control-Allow-Headers",
						value: "*",
					},
				],
			},
		];
	},
};

export default nextConfig;
