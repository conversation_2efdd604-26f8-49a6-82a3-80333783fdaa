name: HermForte Multi-Environment Deployment

on:
  push:
    branches:
      - test
      - staging
      - eaas-test

concurrency: 
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
  
  
jobs:
  deploy-test:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/test' || github.ref == 'refs/heads/staging' || github.ref == 'refs/heads/eaas-test'
    steps:
    - name: Set timezone
      run: sudo timedatectl set-timezone Asia/Shanghai
    - name: Checkout code
      uses: actions/checkout@v2
    - name: Compress build directory
      run: |
        DATE=$(date +%Y%m%d-%H%M%S)
        find . -type f -not -path '*/.git/*' | tar -czvf herm-forte-release-${DATE}.tar.gz -T -

    - name: List files
      run: ls -la
    - name: Copy compressed file to the server
      uses: appleboy/scp-action@master
      with:
        host: >-
          ${{
            github.ref == 'refs/heads/test' && secrets.SERVER_HOST_TEST ||
            github.ref == 'refs/heads/staging' && secrets.SERVER_HOST_STAGING ||
            github.ref == 'refs/heads/eaas-test' && secrets.EAAS_TEST_HOST
          }}
        username: >-
          ${{ 
            github.ref == 'refs/heads/eaas-test' && secrets.EAAS_TEST_SERVER_USER || 
            secrets.SERVER_USER
          }}
        key: ${{ secrets.SSH_KEY }}
        source: "herm-forte-release-*.tar.gz"
        target: "/home/<USER>"
    - name: Deploy to server
      uses: appleboy/ssh-action@master
      with:
        host: >-
          ${{
            github.ref == 'refs/heads/test' && secrets.SERVER_HOST_TEST ||
            github.ref == 'refs/heads/staging' && secrets.SERVER_HOST_STAGING ||
            github.ref == 'refs/heads/eaas-test' && secrets.EAAS_TEST_HOST
          }}
        username: >-
          ${{ 
            github.ref == 'refs/heads/eaas-test' && secrets.EAAS_TEST_SERVER_USER || 
            secrets.SERVER_USER
          }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          sudo mkdir -p /opt/bak/herm-forte
          sudo mkdir -p /opt/apps/herm-forte
          cd /opt/apps/herm-forte
          sudo rm -rf ./node_modules
          sudo tar -czvf /opt/bak/herm-forte/herm_forte_archive_$(date +%Y-%m-%d-%H%M%S).tar.gz ./* && echo "File backup succeeded."
          sudo find . -mindepth 1 -delete
          sudo mv /home/<USER>/herm-forte-release-*.tar.gz ./
          sudo tar -xzvf herm-forte-release-*.tar.gz
          sudo rm -rf herm-forte-release-*.tar.gz
          sudo pnpm i
          if [ "${{ github.ref }}" == "refs/heads/test" ]; then
            sudo pnpm run build:test
          elif [ "${{ github.ref }}" == "refs/heads/staging" ]; then
            sudo pnpm run build:staging
          elif [ "${{ github.ref }}" == "refs/heads/eaas-test" ]; then
            sudo pnpm run build:eaas
          fi
          if [ "${{ github.ref }}" == "refs/heads/test" ]; then
            sudo pm2 start ecosystem.test.config.cjs
          elif [ "${{ github.ref }}" == "refs/heads/staging" ]; then
            sudo pm2 start ecosystem.staging.config.cjs
          elif [ "${{ github.ref }}" == "refs/heads/eaas-test" ]; then 
            sudo pm2 start ecosystem.eaas.test.config.cjs
          fi

          