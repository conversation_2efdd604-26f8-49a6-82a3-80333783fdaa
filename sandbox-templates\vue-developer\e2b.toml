# This is a config for E2B sandbox template.
# You can use 'template_id' (4sgwufi7skp03emzvbwm) or 'template_name (vue-developer) from this config to spawn a sandbox:

# Python SDK
# from e2b import Sandbox
# sandbox = Sandbox(template='vue-developer')

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create({ template: 'vue-developer' })

team_id = "460355b3-4f64-48f9-9a16-4442817f79f5"
start_cmd = "cd /home/<USER>"
dockerfile = "e2b.Dockerfile"
template_name = "vue-developer"
template_id = "4sgwufi7skp03emzvbwm"
