import { Tool } from 'openai-function-calling-tools'
import { z } from 'zod';
import { toolInfoMap, TOOL_NAME } from './tool-info';

function createGetXTwitterInfo() {

  const toolName = TOOL_NAME.GET_X_TWITTER_INFO;
  const paramsSchema = z.object({
    X_TWITTER_URL: z.string().describe(toolInfoMap.get(toolName)!.params.X_TWITTER_URL),
  });

  const execute = async ({ X_TWITTER_URL }: z.infer<typeof paramsSchema>) => {

    let data: any = {};

    const xInfo = extractUsernameAndTweet(X_TWITTER_URL);


    //如果是 x/twitter.com
    if (xInfo) {

      try {
        data.userInfo = await getXUserInfo(xInfo.userID);
      } catch (error) {
        console.log(error);
      }

      if (xInfo.tweetID) {

        try {
          data.tweetContent = await getXTweetByID(xInfo.tweetID);
        } catch (error) {
          console.log(error);
        }


      } else {

        try {
          data.tweetsList = await getXTweetsListByID(xInfo.userID);
        } catch (error) {
          console.log(error);
        }

      }

    }
    return `## X/Twitter Data: ${JSON.stringify(data)} \n\n
# MUST display the original tweet content When the user requests to display the tweet.
  - MUST display the video(s) that contains in the tweet with <video> tag.
  - MUST display the image(s) that contains in the tweet with markdown image syntax.
    `;

  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;
}

export { createGetXTwitterInfo };


function extractUsernameAndTweet(url: string): { userID: string, tweetID?: string } | null {
  const match = url.match(/(?:x\.com|twitter\.com)\/([^\/]+)(\/status\/(\d+))?/);
  if (match) {
    const userID = match[1];
    const tweetID = match[3];
    return {
      userID,
      tweetID,
    };
  }
  return null;
}

const xHeaders = {
  Authorization: `Bearer ${process.env.X_KEY}`,
  "scene-key": process.env.X_SCENE_KEY || "",
};


function generateQueryByJSON(queryParams: { [key: string]: any }) {
  return new URLSearchParams(queryParams).toString();
}

async function getXTweetByID(tweetID: string) {
  const queryString = generateQueryByJSON({ id: tweetID });
  const res = await fetch(`${process.env.X_URL}/user/tweet?${queryString}`, {
    headers: xHeaders
  });
  if (!res.ok) {
    console.log('getXTweetByID--->');
    console.log(res);
    throw new Error(`getXTweetByID HTTP error!: ${JSON.stringify(res)}`);
  }
  return await res.json();
}

async function getXTweetsListByID(tweetID: string) {
  const queryString = generateQueryByJSON({ id: tweetID });
  const res = await fetch(`${process.env.X_URL}/user/tweets?${queryString}`, {
    headers: xHeaders
  });
  if (!res.ok) {
    console.log('getXTweetsListByID--->');
    console.log(res);
    throw new Error(`getXTweetsListByID HTTP error!`);
  }
  return await res.json();
}

async function getXUserInfo(userID: string) {
  const queryString = generateQueryByJSON({ id: userID });
  const res = await fetch(`${process.env.X_URL}/user?${queryString}`, {
    headers: xHeaders
  });
  if (!res.ok) {
    console.log('getXUserInfo--->');
    console.log(res);
    throw new Error(`getXUserInfo HTTP error! `);
  }
  return await res.json();
}