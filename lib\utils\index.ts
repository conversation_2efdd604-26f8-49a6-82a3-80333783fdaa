import { FILE_CATEGORY } from "@/app/api/app/model/appTypes";
import { TimeoutError } from "@/app/api/chat/common/Errors";
import puppeteer from "puppeteer";

import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';

export function getExtension(filename: string) {
    return filename.slice((filename.lastIndexOf(".") - 1 >>> 0) + 2);
}

type AsyncFunctionWithParams<P extends any[], T> = (...args: P) => Promise<T>;


async function resizeImage(inputBuffer: Buffer, maxWidth = 1500) {
    try {
        // 获取图像信息
        const metadata = await sharp(inputBuffer).metadata();
        if (metadata.width && metadata.height) {

            const ratio = metadata.width / metadata.height;
            // 计算新的尺寸
            const newWidth = maxWidth;
            const newHeight = Math.min(8000, Math.round(maxWidth / ratio));
            console.log('screenshot newWidth', newWidth);
            console.log('screenshot newHeight', newHeight);
            const sp = sharp(inputBuffer)
                .resize(newWidth, newHeight, {
                    fit: 'cover',
                    position: 'top',
                    withoutEnlargement: true
                });
            // 调整图片尺寸并保存
            if (process.env.NODE_ENV === 'development') {
                await sp.toFile(`/Users/<USER>/Downloads/${uuidv4()}-screen.jpg`);
            } else {
                await sp.toFile(`/home/<USER>/${uuidv4()}-screen.jpg`);
            }

            const buffer = await sp.toBuffer();
            console.log('图片处理完成');
            return `data:image/jpeg;base64,${buffer.toString('base64')}`;
        }
    } catch (error) {
        console.error('处理图片时出错:', error);
    }
}

export async function screenShotUrl(url: string) {

    console.log('env---', process.env.NODE_ENV);

    let browserConfig: any = {
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
    }
    if (process.env.NODE_ENV === 'development') {
        browserConfig.executablePath = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
    }
    // Create browser instance
    const browser = await puppeteer.launch(browserConfig);


    try {

        console.log('test');
        // Create new page
        const page = await browser.newPage();

        // Set viewport size
        await page.setViewport({
            width: 1920,
            height: 1080
        });

        // Navigate to URL
        await page.goto(url, {
            waitUntil: 'networkidle0',
            timeout: 30000
        });

        //for ubuntu testing
        const screenshotBuffer = await page.screenshot({
            fullPage: true,
            type: 'jpeg',
            quality: 70
        });

        // Ensure the screenshotBuffer is a Buffer type before passing to resizeImage
        const base64String = await resizeImage(Buffer.from(screenshotBuffer));
        return base64String;

    } catch (error) {
        console.error('Screenshot error:', error);
        throw error;
    } finally {
        await browser.close();
    }
}

export function decodeFromUnicode(str: string) {
    return str.replace(/u[0-9a-fA-F]{4}/g, match => {
        return String.fromCharCode(parseInt(match.slice(1), 16));
    });
}


export function withTimeout<P extends any[], T>(
    fn: AsyncFunctionWithParams<P, T>,
    timeoutMs: number
): AsyncFunctionWithParams<P, T> {
    return (...args: P) => {
        return new Promise<T>((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new TimeoutError(`Function execution timed out after ${timeoutMs} ms`));
            }, timeoutMs);

            fn(...args)
                .then((result) => {
                    clearTimeout(timeoutId);
                    resolve(result);
                })
                .catch((error) => {
                    clearTimeout(timeoutId);
                    reject(error);
                });
        });
    };
}


export function getFileExt(filename: string) {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
}

export function getFileCategory(fileString: string) {
    const ext = getFileExt(fileString)
    switch (ext) {
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
            return FILE_CATEGORY.IMAGE
        case 'doc':
        case 'docx':
        case 'pdf':
        case 'ppt':
        case 'pptx':
        case 'txt':
        case 'rtf':
        case 'html':
        case 'htm':
        case 'md':
            return FILE_CATEGORY.TEXT
        case 'xls':
        case 'xlsx':
        case 'xlsm':
        case 'xlsb':
        case 'xlt':
        case 'xltx':
        case 'xltm':
        case 'csv':
        case 'log':
        case 'xml':
        case 'json':
            return FILE_CATEGORY.DATA
        default:
            return FILE_CATEGORY.TEXT
    }
}



/**
 * Replaces celhive-link patterns in text, keeping the dynamic content after the URL
 * @param text - The input text containing celhive-link patterns
 * @returns The text with celhive-link patterns replaced
 */
export function replaceCelhiveLink(text: string): string {
    // Regular expression to match the celhive-link pattern
    // Matches: ---celhive-link--- followed by optional URL and any text after it
    const celhiveLinkRegex = /---celhive-link---\s*(?:https?:\/\/[^\s]+)?\s*(.*)/g;
    
    return text.replace(celhiveLinkRegex, (match, dynamicContent) => {
        // Check if there's a URL in the match
        const urlMatch = match.match(/https?:\/\/[^\s]+/);
        
        if (urlMatch) {
            // URL exists, use the original format
            return `Above is all webpage content from ${urlMatch[0]}, ${dynamicContent}
        ## DO NOT call any tools, just use the content to answer the question
        `;
        } else {
            // No URL, use the alternative format
            return `Based on the above content, answer the question: "${dynamicContent}"
        ## DO NOT call any tools, just use the content to answer the question
        `;
        }
    });
}