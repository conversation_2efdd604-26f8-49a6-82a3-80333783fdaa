import OpenAI from 'openai';
import { Tool } from 'openai-function-calling-tools'
import { z } from 'zod';
import { toolInfoMap, TOOL_NAME } from './tool-info';


function createGetXTwitterNewsSearch() {

  const toolName = TOOL_NAME.GET_X_TWITTER_NEWS_SEARCH;
  const paramsSchema = z.object({
    keyword: z.string().describe(toolInfoMap.get(toolName)!.params.keywords),
    startDate: z.string().describe(toolInfoMap.get(toolName)!.params.startDate),
    endDate: z.string().describe(toolInfoMap.get(toolName)!.params.endDate),
  });

  const execute = async ({ keyword, startDate, endDate }: z.infer<typeof paramsSchema>) => {
    try {
      let data: any = {};
      const tweetsListData = await getXTweetsByKeywords(keyword,);
      const list = tweetsListData?.data?.list || []
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_BASE_URL,
        dangerouslyAllowBrowser: true,
      });
      const prompt = `
  Please generate a summary based on the following tweet array.
  The output is a json array, each item of the array is an object, and there are three attributes inside the object: title, summary, resource. title is the title of the summary, the content of the title is the content of the summary of the specific aspects of the content, such as: ‘AI Research and Development’, summary is the content of the generated summary, resource is an array, each item of the array is an object, resource is the data used to generate the summary from the Twitter array. The content of the summary, resource is an array, the array is only one item, each item of the array is an object, resource is the data source used to generate the summary from the Twitter array.
  For example:
  {
    summaryList: [
      {
      "title":"AI Research and Development",
      "summary":"OpenAI, a leader in artificial intelligence research based in San Francisco, continues to make progress in various AI projects. They are known for innovations like Jukebox (an AI-powered music generator) and a dextrous robotic hand trained to solve the Rubik's Cube.",
      "resource":[
        {
              "id": "1823742501884453312",
              "createdAt": "Wed Aug 14 15:24:30 +0000 2024",
              "content": "Haters will say this is AI  🕺🕺 https://t.co/vqWVxiYXeD",
              "viewCount": 141093458,
              "likeCount": 1891514,
              "retweetCount": 231842,
              "replyCount": 105424,
              "quoteCount": 25217,
              "url": "https://x.com/elonmusk/status/1823742501884453312",
              "user": {
                "name": "Elon Musk",
                "userId": "elonmusk",
                "avatar": "https://pbs.twimg.com/profile_images/1815749056821346304/jS8I28PL_normal.jpg"
              },
              "media": [
                {
                  "id": "13_1823742429050417152",
                  "source": [
                    {
                      "content_type": "application/x-mpegURL",
                      "url": "https://video.twimg.com/amplify_video/1823742429050417152/pl/qZfvusKKN-rRG5yw.m3u8?tag=16"
                    },
                    {
                      "bitrate": 632000,
                      "content_type": "video/mp4",
                      "url": "https://video.twimg.com/amplify_video/1823742429050417152/vid/avc1/320x568/KXwIbteBZtpaNdG5.mp4?tag=16"
                    },
                    {
                      "bitrate": 950000,
                      "content_type": "video/mp4",
                      "url": "https://video.twimg.com/amplify_video/1823742429050417152/vid/avc1/480x852/mxV-aWMFtQ5TB1gC.mp4?tag=16"
                    },
                    {
                      "bitrate": 2176000,
                      "content_type": "video/mp4",
                      "url": "https://video.twimg.com/amplify_video/1823742429050417152/vid/avc1/720x1280/Jjkm71VS_QRjWT3p.mp4?tag=16"
                    }
                  ],
                  "type": "video"
                }
              ]
            },
       ]
      }
    ]
  }

   The array of tweets is: ${list}
      `;

      return await openai.chat.completions.create({
        model: 'gpt-3.5-turbo-0125',
        stream: false,
        response_format: {
          type: "json_object",
        },
        messages: [
          {
            role: 'user',
            content: prompt,
          }
        ],
      }).then((completion) => {
        const { content } = completion.choices[0].message;
        const parsed = JSON.parse(content || "{}");
        parsed.tweetsList = list.slice(0, 5);
        return JSON.stringify(parsed);
      }).catch((error) => { console.log(error) });
    } catch (error) {
      console.log('error', error)
    }
  };

  return new Tool(
    paramsSchema,
    toolName,
    toolInfoMap.get(toolName)!.description,
    execute
  ).tool;

}

export { createGetXTwitterNewsSearch };

const xHeaders = {
  Authorization: `Bearer ${process.env.X_KEY}`,
  "scene-key": process.env.X_SCENE_KEY || "",
};

function generateQueryByJSON(queryParams: { [key: string]: any }) {
  return new URLSearchParams(queryParams).toString();
}

async function getXTweetsByKeywords(keywords: string) {
  const queryString = generateQueryByJSON({ type: 'Top', fuzzyKeywords: keywords });
  const res = await fetch(`${process.env.X_URL}/search/advanced?${queryString}`, {
    headers: xHeaders
  });
  if (!res.ok) {
    throw new Error(`HTTP error! status: ${res.status}`);
  }
  return await res.json();
}