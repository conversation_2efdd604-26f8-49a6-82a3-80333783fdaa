import { metadata } from './../../../layout';
import { LongRunningTaskOpenAI } from "../openai/core/LongRunningTaskOpenAI";
import { LongRunningTaskAnthropic } from "../claude/core/LongRunningTaskAnthropic";
import log from "@/lib/logs";
import { AbortControllerContainer } from "@/lib/core/abort-controller";

export function getResponse(task: LongRunningTaskOpenAI | LongRunningTaskAnthropic, userID: string) {
    let closed = false;
    const stream = new ReadableStream({
        start(controller) {
            const encoder = new TextEncoder();
            

            // 添加计时器记录运行时间
            const startTime = Date.now();

            task.run()
                .catch((error: any) => {
                    console.log('longrunning error', 'userID:', userID, 'time:', new Date().toISOString());
                    controller.close();
                    log({
                        type: 'ERROR',
                        message: error?.error?.metadata?.raw || error?.message || error?.error || error,
                    });
                })

            task.on('progress', (data: any) => {
                controller.enqueue(encoder.encode(data));
            });

            task.on('complete', (data: any) => {
                console.log('Complete event received', new Date().toISOString());
                controller.enqueue(encoder.encode(data));
                if (!closed) {
                    controller.close();
                    closed = true;
                }
            })
        },

        cancel() {
            console.log('Stream cancelled', new Date().toISOString());
            AbortControllerContainer.abort();
            task.cancel();
        }
    });

    console.log("stream-----", stream);

    return new Response(stream, {
        headers: {
            "Content-Type": "text/event-stream; charset=utf-8",
            Connection: "keep-alive",
            "Cache-Control": "no-cache, no-transform",
            "X-Accel-Buffering": "no",
            "Content-Encoding": "none"
        },
    });
}