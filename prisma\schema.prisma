generator client {
  provider        = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model e3_model_data {
  id             BigInt    @id @default(autoincrement())
  user_id        BigInt?
  uuid           String?   @db.VarChar(32)
  b64_json       String?   @db.LongText
  revised_prompt String?   @db.Text
  create_time    DateTime? @default(now()) @db.DateTime(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model file_manage {
  id            BigInt    @id @default(autoincrement())
  uuid          String?   @unique(map: "uuid_index") @db.VarChar(32)
  name          String?   @db.VarChar(50)
  origin_url    String?   @db.VarChar(255)
  thumbnail_url String?   @db.<PERSON>ar<PERSON><PERSON>(255)
  create_time   DateTime? @db.DateTime(0)
  type          Boolean?
  user_id       BigInt?
  format        String?   @db.VarChar(20)
  time_seconds  Float?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_api_key_config {
  id          Int       @id @default(autoincrement())
  api_key     String    @db.VarChar(100)
  remarks     String?   @db.VarChar(50)
  status      Boolean?  @default(true)
  plus_flag   Boolean?  @default(false)
  create_by   String?   @db.VarChar(20)
  create_time DateTime? @default(now()) @db.DateTime(0)
  update_by   String?   @db.VarChar(20)
  update_time DateTime? @default(now()) @db.DateTime(0)
  api_host    String?   @db.VarChar(50)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_context {
  id      BigInt   @id @default(autoincrement())
  title   String   @db.VarChar(100)
  context String?  @db.VarChar(510)
  status  Boolean? @default(true)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_0 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_1 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_10 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_11 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_12 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_13 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_14 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_15 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_16 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_17 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_18 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_19 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_2 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_20 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_21 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_22 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_23 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_24 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_25 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_26 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.Text
  response_file_base64 String?   @db.Text
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_27 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.Text
  response_file_base64 String?   @db.Text
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_28 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_3 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_4 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  attachments          String?   @db.VarChar(4096)
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_5 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_7 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_8 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_chat_record_9 {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt    @default(0)
  type                 Boolean   @default(true)
  api_key_id           BigInt
  api_cost_time        Int?
  question             String?   @db.MediumText
  completion_tokens    Int?
  answer               String?   @db.Text
  create_time          DateTime? @default(now()) @db.DateTime(0)
  mode_id              BigInt?
  context_id           BigInt?
  dialog_id            BigInt?
  relation_id          BigInt?
  tts_language_type_id BigInt?
  tts_type             Boolean?  @default(false)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  attachments          String?   @db.VarChar(4096)
  request_file_base64  String?   @db.LongText
  response_file_base64 String?   @db.LongText
  artifacts            String?   @db.VarChar(256)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_dialog {
  id                   BigInt    @id @default(autoincrement())
  user_id              BigInt?
  name                 String?   @db.VarChar(50)
  group_id             BigInt?
  type                 Boolean?
  create_time          DateTime? @db.DateTime(0)
  del_flag             Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  tts_type             Boolean?
  tts_language_type_id BigInt?
  mode_id              BigInt?
  views                Int?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_group {
  id           BigInt    @id @default(autoincrement())
  name         String?   @db.VarChar(50)
  description  String?   @db.VarChar(255)
  user_id      BigInt?
  default_flag Boolean?
  star_flag    Boolean?
  create_time  DateTime? @db.DateTime(0)
  dialog_count Int?
  deleted      Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_level_config {
  id                      BigInt   @id @default(autoincrement())
  type                    Boolean? @unique(map: "tpye_unique")
  star_group_dialog_count Int?
  group_dialog_count      Int?
  max_group_count         Int?
  chats_daily_count       Int?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_model_error {
  id             BigInt    @id @default(autoincrement())
  mode_id        BigInt
  mode_name      String?   @db.VarChar(50)
  mode_show_name String?   @db.VarChar(50)
  create_time    DateTime? @db.DateTime(0)
  api_host       String?   @db.VarChar(255)
  api_key_suffix String?   @db.VarChar(255)
  request_json   String?   @db.MediumText
  response_json  String?   @db.Text
  http_code      Int?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_openai_mode {
  id            BigInt    @id @default(autoincrement())
  type          Boolean
  name          String?   @db.VarChar(50)
  max_token     Int?
  plus_flag     Boolean?  @default(false)
  create_time   DateTime? @db.DateTime(0)
  deleted       Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  search_flag   Boolean?  @default(false)
  show_name     String?   @db.VarChar(50)
  api_host      String?   @db.VarChar(255)
  api_key       String?   @db.VarChar(255)
  remark        String?   @db.VarChar(255)
  system_prompt String?   @db.VarChar(3000)
  event         Boolean?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_replicate {
  id             BigInt    @id @default(autoincrement())
  file_id        BigInt?
  user_id        BigInt?
  chat_record_id BigInt?
  status         Boolean?
  replicate_id   String?   @db.VarChar(50)
  model          String?   @db.VarChar(50)
  get_url        String?   @db.VarChar(255)
  response_json  String?   @db.Text
  token          String?   @db.VarChar(108)
  create_time    DateTime? @db.DateTime(0)
  finish_time    DateTime? @db.DateTime(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gpt_user_config {
  id                  BigInt    @id @default(autoincrement())
  top_p               Float?
  temperature         Float?
  completed_max_token Int?
  create_time         DateTime? @db.DateTime(0)
  user_id             BigInt?   @unique(map: "user_id_UNIQUE")
  context_count       Int?
}

model gptopenaimode {
  id            Int      @id @default(autoincrement())
  type          Int
  name          String   @db.VarChar(50)
  max_token     Int
  plus_flag     Int
  create_time   DateTime @db.DateTime(0)
  deleted       Boolean
  search_flag   Int
  show_name     String   @db.VarChar(50)
  api_host      String   @db.VarChar(255)
  api_key       String   @db.VarChar(255)
  remark        String   @db.VarChar(255)
  system_prompt String   @db.VarChar(3000)
  event         Int
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model pay_activity {
  id                   BigInt    @id @default(autoincrement())
  name                 String?   @db.VarChar(50)
  description          String?   @db.VarChar(255)
  status               Boolean
  expire_time          DateTime? @db.DateTime(0)
  type                 Boolean
  new_discount_percent Decimal?  @db.Decimal(3, 2)
  old_discount_percent Decimal?  @db.Decimal(3, 2)
  activity_count       Int?
  create_time          DateTime? @db.DateTime(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model pay_activity_img {
  id          BigInt    @id @default(autoincrement())
  activity_id BigInt?
  sort        Boolean?
  url         String?   @db.VarChar(255)
  create_time DateTime? @db.DateTime(0)
  deleted     Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model pay_activity_order {
  id               BigInt    @id @default(autoincrement())
  activity_id      BigInt?
  pay_order_id     BigInt?
  user_id          BigInt?
  status           Boolean?
  user_type        Boolean?
  discount_percent Decimal?  @db.Decimal(3, 2)
  create_time      DateTime? @db.DateTime(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model pay_ali_order {
  id            BigInt    @id @default(autoincrement())
  pay_order_id  BigInt?
  app_id        String?   @db.VarChar(32)
  goods_desc    String?   @db.VarChar(127)
  out_trade_no  String?   @db.VarChar(32)
  trans_time    DateTime? @db.DateTime(0)
  expire_time   DateTime? @db.DateTime(0)
  attach        String?   @db.VarChar(128)
  price         Decimal?  @db.Decimal(16, 2)
  notify_id     String?   @db.VarChar(36)
  notify_time   DateTime? @db.DateTime(0)
  user_id       BigInt?
  status        Boolean?
  refund_status Boolean?  @default(false)
  success_time  DateTime? @db.DateTime(0)
  update_time   DateTime? @db.DateTime(0)
  code          String?   @db.VarChar(32)
  msg           String?   @db.VarChar(256)
  del_flag      Boolean?  @default(false)
  version       Int?
  buyer_id      String?   @db.VarChar(45)
  trans_no      String?   @db.VarChar(45)
  web_form_html String?   @db.Text
  app_order_str String?   @db.Text
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model pay_ali_order_log {
  id               BigInt    @id @default(autoincrement())
  pay_ali_order_id BigInt?
  event            Boolean?
  time             DateTime? @db.DateTime(0)
  status           Boolean?
  refund_status    Boolean?  @default(false)
  code             String?   @db.VarChar(32)
  msg              String?   @db.VarChar(256)
  request_json     String?   @db.Text
  response_json    String?   @db.Text
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model pay_exchange {
  id                    BigInt    @id @default(autoincrement())
  code                  String    @unique(map: "code_unique") @db.VarChar(8)
  package_id            BigInt
  user_id               BigInt?
  level_limit_config_id BigInt?
  expire_time           DateTime  @db.DateTime(0)
  create_user_id        BigInt?
  create_time           DateTime? @db.DateTime(0)
  ticket_no             String?   @db.VarChar(6)
  type                  Boolean?  @default(false)
  exchange_time         DateTime? @db.DateTime(0)
  user_expire_time      DateTime? @db.DateTime(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model pay_order {
  id                          BigInt    @id @default(autoincrement())
  package_id                  BigInt?
  type                        Boolean?
  price                       Decimal?  @db.Decimal(16, 2)
  status                      Boolean?
  success_time                DateTime? @db.DateTime(0)
  create_time                 DateTime? @db.DateTime(0)
  user_id                     BigInt?
  order_no                    String?   @unique(map: "order_no") @db.VarChar(30)
  commission                  Decimal?  @db.Decimal(16, 2)
  deduction_amt               Decimal?  @default(0.00) @db.Decimal(16, 2)
  commission_amt              Decimal?  @default(0.00) @db.Decimal(16, 2)
  deduction_flag              Boolean?  @default(false)
  commission_flag             Boolean?  @default(false)
  from_flag                   Boolean?
  payment_identification_text String?   @db.Text
  expire_time                 DateTime? @db.DateTime(0)
  discount_amt                Decimal?  @default(0.00) @db.Decimal(16, 2)
  activity_id                 BigInt?
  renewals_flag               Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  before_renewals_expire_time DateTime? @db.DateTime(0)
  days                        Int?
  original_order_id           BigInt?

  @@index([status, create_time], map: "status_create_time")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model pay_package {
  id                    BigInt    @id @default(autoincrement())
  title                 String?   @db.VarChar(50)
  limit_use             Int?
  expire_days           Int?
  price                 Decimal?  @db.Decimal(10, 2)
  type                  Boolean?
  description           String?   @db.VarChar(255)
  create_time           DateTime? @db.DateTime(0)
  plus_flag             Boolean?
  level                 Int?
  level_limit_config_id BigInt?
  deleted               Boolean?  @default(dbgenerated("b'0'")) @db.Bit(1)
  cashback_rate         Decimal?  @db.Decimal(2, 2)
  activity_id           BigInt?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model pay_stripe_order {
  id              BigInt    @id @default(autoincrement())
  pay_order_id    BigInt?
  goods_desc      String?   @db.VarChar(127)
  trans_time      DateTime? @db.DateTime(0)
  expire_time     DateTime? @db.DateTime(0)
  price           Decimal?  @db.Decimal(16, 2)
  notify_time     DateTime? @db.DateTime(0)
  user_id         BigInt?
  status          Boolean?
  refund_status   Boolean?  @default(false)
  success_time    DateTime? @db.DateTime(0)
  update_time     DateTime? @db.DateTime(0)
  code            String?   @db.VarChar(32)
  msg             String?   @db.VarChar(256)
  version         Int?
  url             String?   @db.VarChar(255)
  del_flag        Boolean?  @default(false)
  session_id      String?   @db.VarChar(255)
  customer_email  String?   @db.VarChar(50)
  cardholder_name String?   @db.VarChar(20)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model pay_stripe_order_log {
  id                  BigInt    @id @default(autoincrement())
  pay_stripe_order_id BigInt?
  event               Boolean?
  time                DateTime? @db.DateTime(0)
  status              Boolean?
  refund_status       Boolean?  @default(false)
  code                String?   @db.VarChar(32)
  msg                 String?   @db.VarChar(256)
  request_json        String?   @db.Text
  response_json       String?   @db.Text
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model pay_wx_order {
  id            BigInt    @id @default(autoincrement())
  pay_order_id  BigInt?
  app_id        String?   @db.VarChar(32)
  mch_id        String?   @db.VarChar(32)
  goods_desc    String?   @db.VarChar(127)
  out_trade_no  String?   @db.VarChar(32)
  wx_order_id   String?   @db.VarChar(32)
  trans_time    DateTime? @db.DateTime(0)
  expire_time   DateTime? @db.DateTime(0)
  attach        String?   @db.VarChar(128)
  price         Decimal?  @db.Decimal(16, 2)
  notify_time   DateTime? @db.DateTime(0)
  prepay_id     String?   @db.VarChar(64)
  user_id       BigInt?
  status        Boolean?
  refund_status Boolean?  @default(false)
  success_time  DateTime? @db.DateTime(0)
  update_time   DateTime? @db.DateTime(0)
  payer_open_id String?   @db.VarChar(64)
  code          String?   @db.VarChar(32)
  msg           String?   @db.VarChar(256)
  del_flag      Boolean?  @default(false)
  version       Int?
  web_qr_url    String?   @db.VarChar(255)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model pay_wx_order_log {
  id              BigInt    @id @default(autoincrement())
  pay_wx_order_id BigInt?
  event           Boolean?
  time            DateTime? @db.DateTime(0)
  status          Boolean?
  refund_status   Boolean?  @default(false)
  code            String?   @db.VarChar(32)
  msg             String?   @db.VarChar(256)
  request_json    String?   @db.Text
  response_json   String?   @db.Text
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model sensitive_detection_record {
  id          Int       @id @default(autoincrement())
  user_id     Int?
  data_id     String?   @db.VarChar(50)
  task_id     String?   @db.VarChar(50)
  content     String?   @db.Text
  label       Int?
  sub_label   String?   @db.VarChar(50)
  create_time DateTime? @default(now()) @db.DateTime(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model sys_user {
  id          BigInt    @id @default(autoincrement())
  name        String?   @db.VarChar(50)
  account     String?   @unique(map: "account_unique") @db.VarChar(50)
  password    String?   @db.VarChar(100)
  status      Boolean?  @default(true)
  last_login  DateTime? @db.DateTime(0)
  create_time DateTime? @db.DateTime(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model tts_api_key {
  id          BigInt    @id @default(autoincrement())
  api_key     String?   @db.VarChar(50)
  area        String?   @db.VarChar(20)
  create_time DateTime? @db.DateTime(0)
  remark      String?   @db.VarChar(255)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model tts_chat_file {
  id             BigInt    @id @default(autoincrement())
  chat_record_id BigInt?
  type           Boolean?
  file_id        BigInt
  chats          String?   @db.Text
  user_id        BigInt?
  create_time    DateTime? @db.DateTime(0)
  index          Int?
  factor         Float?    @default(0.00)
  seconds        Float?    @default(0.00)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model tts_language_type {
  id                 BigInt    @id @default(autoincrement())
  name               String?   @db.VarChar(20)
  lang               Boolean?
  plus_flag          Boolean?
  status             Boolean?
  voice_name         String?   @db.VarChar(50)
  create_time        DateTime? @db.DateTime(0)
  lang_desc          String?   @db.VarChar(45)
  studio_chat_length Int?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user {
  id                    BigInt    @id @default(autoincrement())
  name                  String?   @db.VarChar(50)
  phone                 String?   @unique(map: "phone_unique") @db.VarChar(20)
  status                Boolean?  @default(true)
  plus_flag             Boolean?  @default(false)
  plus_expire_time      DateTime? @db.DateTime(0)
  last_login            DateTime? @db.DateTime(0)
  create_time           DateTime? @db.DateTime(0)
  file_id               BigInt?
  commission            Decimal?  @default(0.00) @db.Decimal(16, 2)
  invite_code           String?   @unique(map: "invite_code_unique") @db.VarChar(8)
  up_invite_id          BigInt?
  package_id            BigInt?
  level_limit_config_id BigInt?
  freeze_commission     Decimal?  @default(0.00) @db.Decimal(16, 2)
  sms_sent              Boolean?  @default(false)
  exchange_id           BigInt?
  email                 String?   @unique(map: "email_unique") @db.VarChar(50)
  google_user_id        String?   @db.VarChar(30)
  google_user_name      String?   @db.VarChar(50)
  google_email          String?   @db.VarChar(50)
  github_user_id        String?   @db.VarChar(20)
  github_user_name      String?   @db.VarChar(50)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_commission_log {
  id           BigInt   @id @default(autoincrement())
  user_id      BigInt
  pay_order_id BigInt?
  status       Boolean? @default(false)
  type         Boolean? @default(false)
  amount       Decimal? @default(0.00) @db.Decimal(16, 2)
  from_user_id BigInt?
  package_id   BigInt?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_deduction_pay_log {
  id               BigInt   @id @default(autoincrement())
  user_id          BigInt
  pay_order_id     BigInt?
  deduction_days   Int
  deduction_price  Decimal? @db.Decimal(16, 2)
  og_package_id    BigInt?
  og_expire_days   Int?
  og_price         Decimal? @db.Decimal(16, 2)
  og_package_level Int?
  status           Boolean? @default(false)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_level_limit_config {
  id                       BigInt   @id @default(autoincrement())
  voice                    Int?
  start_group_dialog_limit Int?
  group_dialog_limit       Int?
  dialog_limit             Int?
  group_limit              Int?
  type                     Boolean?
  e3                       Int?
  period                   Boolean?
  gpt_forte_preview        Int?
  gpt_piano_preview        Int?
  gpt_mezzo_preview        Int?
  claude_mezzo_preview     Int?
  claude_opus              Int?
  mixtral_forte_preview    Int?
  meta_llama               Int?
  gemini                   Int?
  gemini_flash             Int?
  deepseek                 Int?
  gpt4o                    Int?
  lab_claude_opus          Int?
  stable_image_ultra       Int?
  claude_sonnet            Int?
  gpt4o_mini               Int?
  meta_llama405b           Int?
  replicate_flux_pro       Int?
  gemini_pro0801           Int?
  ideogram                 Int?
  o1_preview               Int?
  o1_mini                  Int?
  lepton_audio             Int?
  gpt4o_audio              Int?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_redeem_code {
  id             BigInt    @id @default(autoincrement())
  redeem_code    String    @unique(map: "redeem_code_unique") @db.VarChar(8)
  expire_time    DateTime  @db.DateTime(0)
  redeem_user_id BigInt?
  create_user_id BigInt?
  package_id     BigInt?
  redeem_time    DateTime? @db.DateTime(0)
}
