{"name": "herm-forte", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build:test": "env-cmd -f .env.test next build", "build:staging": "env-cmd -f .env.staging next build", "build:preview": "env-cmd -f .env.preview next build", "build:eaas": "env-cmd -f .env.e<PERSON><PERSON><PERSON> next build", "build:production": "env-cmd -f .env.production next build", "start:test": "env-cmd -f .env.test next start --keepAliveTimeout 600000 -p 9096", "start:staging": "env-cmd -f .env.staging next start --keepAliveTimeout 600000 -p 9096", "start:preview": "env-cmd -f .env.preview next start --keepAliveTimeout 600000 -p 9096", "start:eaas": "env-cmd -f .env.e<PERSON><PERSON><PERSON> next start --keepAliveTimeout 600000 -p 9096", "start:production": "env-cmd -f .env.production next start --keepAliveTimeout 600000 -p 9096", "pm2": "pm2 start ecosystem.config.cjs", "lint": "next lint", "db:generate": "prisma generate --schema=./prisma/schema.prisma", "db:migrate": "prisma migrate dev --schema=./prisma/schema.prisma --skip-seed", "db:reset": "prisma migrate reset --schema=./prisma/schema.prisma --skip-seed", "db:push": "prisma db push --schema=./prisma/schema.prisma", "db:seed": "prisma db seed", "db:studio": "prisma studio --schema=./prisma/schema.prisma", "postinstall": "prisma generate"}, "dependencies": {"@ai-sdk/anthropic": "^1.1.16", "@ai-sdk/openai": "^0.0.13", "@ai-sdk/ui-utils": "^1.1.18", "@anthropic-ai/sdk": "^0.52.0", "@e2b/code-interpreter": "^1.0.4", "@mermaid-js/mermaid-cli": "^11.4.2", "@prisma/client": "^5.22.0", "@supabase/supabase-js": "^2.49.1", "@upstash/ratelimit": "^2.0.5", "@vercel/edge-config": "^1.4.0", "@vercel/kv": "^3.0.0", "ai": "^4.3.19", "axios": "^1.8.3", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "form-data": "^4.0.2", "install": "^0.13.0", "ioredis": "^5.6.0", "jsdom": "^26.0.0", "lodash": "^4.17.21", "mermaid": "^11.4.1", "next": "14.2.3", "openai": "^4.87.3", "openai-function-calling-tools": "^6.8.0", "posthog-js": "^1.230.2", "prisma": "^5.22.0", "puppeteer": "^24.4.0", "react": "^18.3.1", "react-dom": "^18.3.1", "replicate": "^1.0.1", "sharp": "^0.33.5", "tailwind-merge": "^2.6.0", "timezone": "link:dayjs/plugin/timezone", "utc": "link:dayjs/plugin/utc", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.2"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.16", "@types/node": "^20.17.24", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "env-cmd": "^10.1.0", "eslint": "^8.57.1", "eslint-config-next": "14.2.3", "typescript": "^5.8.2"}}