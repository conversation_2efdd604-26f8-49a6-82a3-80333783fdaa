import { Sandbox, SandboxOpts } from '@e2b/code-interpreter';
import log from '../logs';

/**
 * SandBox class that manages the sandboxes for the users.
 */
export class SandBoxManager {
    private userID: string;
    sandBox: Sandbox | null;

    /**
     * Creates a new instance of the SandBox class.
     * @param userID The user ID associated with the sandbox.
     */
    constructor(userID: string) {
        if (!userID) throw new Error('Invalid user ID');
        this.userID = userID;
        this.sandBox = null;
    }

    /**
     * Initializes the sandbox for the user.
     * @returns A Promise that resolves to the sandbox manager.
     */
    async initSandBox(): Promise<SandBoxManager> {
        if (!this.sandBox) {
            this.sandBox = await this.createSandbox();
        }
        return this;
    }

    /**
     * Closes the sandbox if it exists.
     */
    async close() {
        if (this.sandBox) {
            await this.sandBox.kill();
            this.sandBox = null;
        }
    }

    async runCode(code: string, opts: SandboxOpts & { extraDependencies?: string[] } = {}) {
        if (!this.sandBox) {
            throw new Error('Sandbox not initialized');
        }

        let fullCode = code;
        if (opts.extraDependencies && opts.extraDependencies.length > 0) {
            const installCommand = `!pip install ${opts.extraDependencies.join(' ')}`;
            fullCode = `${installCommand}\n${fullCode}`;
        }

        return this.sandBox.runCode(fullCode, opts);
    }

    private async createSandbox(): Promise<Sandbox> {
        const apiKey = process.env.E2B_API_KEY;

        // Check for existing sandbox for the user
        const runningSandboxes = await Sandbox.list({ apiKey });
        const found = runningSandboxes.find(s => s.metadata?.userID === this.userID)

        let sBox: Sandbox;

        if (found) {
            sBox = await Sandbox.connect(found.sandboxId);
            log({
                type: 'COMMON',
                message: {
                    content: 'Reconnected to existing sandbox',
                    sandboxID: found.sandboxId,
                    userID: found.metadata?.userID
                }
            });
        } else {
            sBox = await Sandbox.create(
                process.env.E2B_CODE_INTERPRETER_TEMPLATE!,
                {
                    metadata: { userID: this.userID },
                    timeoutMs: 3_600_000,
                }
            );
            log({
                type: 'COMMON',
                message: {
                    content: 'Created new sandbox:',
                    userID: this.userID
                }
            });
        }
        sBox.setTimeout(3_600_000);
        return sBox;
    }
}

export async function createSandBoxManager(userID: string): Promise<SandBoxManager> {
    const sandBoxManager = new SandBoxManager(userID);
    return await sandBoxManager.initSandBox();
}
