import { fail, success } from '@/lib/core/response'
import { decodeFromUnicode } from '@/lib/utils';
import { createGenerateSmilesFromMolecule } from '@/lib/tools/generate-smiles-from-molecule';
import { FunctionDefinition } from 'openai/resources';

export async function POST(req: Request) {
  const {
    molecule
  }: { molecule: string; } =
    await req.json()

  try {
    const decodedMolecule = decodeFromUnicode(molecule);
    const runSmilesGenerator = (createGenerateSmilesFromMolecule() as [Function, FunctionDefinition])[0];
    const rawResult = await runSmilesGenerator({ molecule: decodedMolecule });
    const parsedResult = JSON.parse(rawResult);

    if (parsedResult.smiles && !parsedResult.smiles.includes('Could not resolve'))
      return success("\n```smiles\n" + parsedResult.smiles + "\n```\n");
    else
        return fail('Invalid molecule');
  } catch (error) {
    return fail('Invalid molecule');
  }
}
