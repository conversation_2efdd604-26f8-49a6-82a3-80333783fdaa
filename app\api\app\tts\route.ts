import { NextRequest, NextResponse } from 'next/server';
import { HttpStatusCode } from 'axios';
import { fail } from '@/lib/core/response';

export async function POST(req: NextRequest) {
    try {
        const requestData = await req.json();

        // 验证请求数据
        if (!requestData.messages || !Array.isArray(requestData.messages)) {
            return fail('无效的请求格式', HttpStatusCode.BadRequest);
        }

        // 构建请求体
        const requestBody = {
            model: "Meta-Llama-3.1-8B-Instruct",
            max_tokens: 4096,
            temperature: 0.5,
            top_p: 0.8,
            stream: true,
            require_audio: true,
            tts_preset_id: "sean",
            ...requestData,
        };

        // 发送请求到 Lepton API
        const leptonResponse = await fetch('https://llama3-1-8b.lepton.run/api/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.LEPTON_API_KEY}`
            },
            body: JSON.stringify(requestBody)
        });

        if (!leptonResponse.ok) {
            throw new Error(`API 请求失败，状态码：${leptonResponse.status}`);
        }

        // 直接返回 Lepton API 的响应
        return new NextResponse(leptonResponse.body, {
            status: leptonResponse.status,
            headers: {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
            },
        });

    } catch (error) {
        console.error('处理请求时出错:', error);
        return fail('内部服务器错误', HttpStatusCode.InternalServerError);
    }
}
