import OpenAI from 'openai';
import { MessageParam } from '@anthropic-ai/sdk/resources/messages';
import { ChatCompletionMessageParam } from 'openai/resources/chat/completions';

export const getRelatedSearchQuery = async ({
  messages,
}: {
  messages: MessageParam[] | ChatCompletionMessageParam[];
}) => {
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_BASE_URL,
    dangerouslyAllowBrowser: true,
  });
  const prompt = `
  Please generate three follow-up questions related to the user's original question based on the relevant context. 
  Ensure that these questions are specific, can be independently raised, and include relevant details such as the name of the event, location, etc. 
  Your output needs to be a JSON array containing 3 strings. Each string represents a question. Please ensure that your output is meaningful and relevant to the original questions and related context.
  The returned language must be based on the language of the original conversation.
  for example:
  {
    related_search_queries: [
      "What do you think of the location of this event?",
      "What do you think of the theme of this event?" ,
      "What do you think of the time arrangement for this event?"
    ]
  }

  Context / Original Question:
  """
  ${JSON.stringify(messages, null, 2)}
  """
  `;


  return await openai.chat.completions.create({
    model: 'gpt-4o-mini',
    stream: false,
    response_format: {
      type: "json_object",
    },
    messages: [
      {
        role: 'user',
        content: prompt,
      }
    ],
  }).then((completion) => {
    const { content } = completion.choices[0].message;
    const parsed = JSON.parse(content || "{}");

    return parsed.related_search_queries;
  });
}
