import Anthropic from "@anthropic-ai/sdk";
import { MessageCreateParamsBase } from "@anthropic-ai/sdk/resources/messages";
import { CLAUDE_SYS_PROMPT, CLAUDE_THINKING, generateCommonSysPrompt, generateInstructionBasedOnPrompt } from "../../common/prompt-instruction";
import { omit } from "lodash";
import { TOOL_NAME } from "@/lib/tools/tool-info";
import { replaceCelhiveLink } from "@/lib/utils";


function replaceArtifactsInContent(content: string, artifacts: any[]) {
    if (!content || !artifacts?.length) return content;

    let result = content;
    let index = 0;

    result = result.replace(/<juchats-artifacts>.*?<\/juchats-artifacts>/g, () => {
        const artifact = artifacts[index];
        index++;
        return `

-------------artifact with id ${artifact?.id} start-----------------------
## here is the generated artifact，NEVER show the following artifacts to the user: 
- **id**: ${artifact?.id}
- **title**: ${artifact?.title}
- **description**: ${artifact?.description}
- **html**: ${artifact?.html}
-------------artifact with id ${artifact?.id} end-----------------------

        ` || '';
    });

    return result;
}

export const getCompletion = async (
    body: MessageCreateParamsBase & { deepThinking?: boolean },
    client: Anthropic,
    userID: string
) => {
    let SystemPrompt = `
    ${generateCommonSysPrompt(body.model)}
    ${CLAUDE_SYS_PROMPT}
    `
    if (body.deepThinking && (body.deepThinking === true || body.deepThinking === "true")) {
        SystemPrompt = `
        ${CLAUDE_THINKING}
        ${SystemPrompt}
        `
    } else {
        SystemPrompt = `
        ${SystemPrompt}
        ## NEVER do any thinking!(Do not express any thinking)
        `
    }

    const newBody = omit(body, ['deepThinking']);

    if (newBody.messages && newBody.messages.length > 0) {
        const lastMessage = newBody.messages[newBody.messages.length - 1];
        if (lastMessage.role === 'user') {
            if (typeof lastMessage.content === 'string') {
                lastMessage.content = replaceCelhiveLink(lastMessage.content);
                lastMessage.content = generateInstructionBasedOnPrompt(lastMessage.content, newBody.model);
            } else if (Array.isArray(lastMessage.content)) {
                lastMessage.content.forEach((content: any) => {
                    if (content.type === 'text') {
                        content.text = replaceCelhiveLink(content.text);
                        content.text = generateInstructionBasedOnPrompt(content.text, newBody.model);
                    }
                });
            }
        }
    }


    //处理 messages 中的 artifacts
    if (newBody.messages && newBody.messages.length > 0) {
        newBody.messages.forEach((message: any) => {
            if (message.role === 'assistant') {
                const artifacts = message.artifactsList;
                const content = message.content;
                // remove the artifactsList from the message
                delete message.artifactsList;
                if (typeof content === 'string') {
                    const contentFilledArtifacts = replaceArtifactsInContent(content, artifacts);
                    message.content = contentFilledArtifacts;
                }
            }
        });
    }

    let response;

    console.log('body-claude-messages---->', 'userID:', userID, JSON.stringify(newBody.messages))

    if (
        newBody.model.startsWith("claude-3-7-sonnet-") ||
        newBody.model.startsWith("claude-sonnet-4") ||
        newBody.model.startsWith("claude-opus-4")) {
        console.log('-----------------claude 3.7 or 4 or opus 4 sonnet.')
        response = await client.messages.create({
            system: [
                {
                    type: 'text',
                    text: SystemPrompt
                },
                {
                    type: 'text',
                    text: `
## DO NOT SHOW any code blocks in thinking process.
## WHEN generate mermaid diagrams
    -MUST state what needs to be done, and then call the tool.
    -DO NOT return the mermaid code directly,MUST call the tool.
    -DO NOT SHOW the result of the tool.
    -DO NOT SHOW the mermaid.ink link to the user.
                    `
                }
                , {
                    type: 'text',
                    text: `## **Generate Artifacts**: 
    - Use \`${TOOL_NAME.GENERATE_ARTIFACTS}\` tool function to generate artifacts.
    - It is only invoked when users explicitly request to generate some fancy and professional visual effects. Among them, the following situations must be invoked: web pages, SVG animations, and interactive charts.
    - DO NOT return the html code to the user directly, instead call the tool.
    - MUST call \`${TOOL_NAME.GENERATE_ARTIFACTS}\` tool when user want to generate webpage ,svg animation, webgl(three.js),interactive charts,etc. DO NOT call other tool functions.
    - If the user wants to generate multiple artifacts (like more than one webpages、svgs), MUST call the tool one by one to generate them and MUST give some instructions for each artifact before calling the tool.
                        `
                }
                , {
                    type: 'text',
                    text: 'MUST return well-formatted and clearly-structured perfect Markdown format, and generate articles in the best and most professional way (with clear logic and in-depth explanations for professional content).'
                }
            ],

            ...newBody,
            max_tokens: newBody.model.startsWith("claude-opus-4") ? 32000 : 64000,
            thinking: {
                type: "enabled",
                budget_tokens: newBody.model.startsWith("claude-sonnet-4") ? 5000 : 10000
            },
            temperature: 1,
        }, {
            timeout: 600000
        })
    } else {

        const syss: Anthropic.Messages.TextBlockParam[] = [
            {
                type: 'text',
                text: SystemPrompt
            }, {
                type: 'text',
                text: 'MUST return well-formatted and clearly-structured perfect Markdown format, and generate articles in the best and most professional way (with clear logic and in-depth explanations for professional content).'
            }
        ]

        if (newBody.model.startsWith("claude-3-5-sonnet-")) {
            syss.push({
                type: 'text',
                text: `## **Generate Artifacts**: 
- Use \`${TOOL_NAME.GENERATE_ARTIFACTS}\` tool function to generate artifacts.
- It is only invoked when users explicitly request to generate some fancy and professional visual effects. Among them, the following situations must be invoked: web pages, SVG animations, and interactive charts.
- DO NOT return the html code to the user directly, instead call the tool.
- MUST call \`${TOOL_NAME.GENERATE_ARTIFACTS}\` tool when user want to generate webpage ,svg animation, webgl(three.js) . DO NOT call other tool functions.
- If the user wants to generate multiple artifacts (like more than one webpages、svgs), MUST call the tool one by one to generate them and MUST give some instructions for each artifact before calling the tool.
                    `
            })
        }


        response = await client.messages.create({
            system: syss,
            ...newBody,
        }, {
            timeout: 600000
        })
    }






    return response;
}
