# This is a config for E2B sandbox template.
# You can use template ID (w83rwcbbai8nu9wxhcqx) or template name (nextjs-developer-vercel-test) to create a sandbox:

# Python SDK
# from e2b import Sandbox, AsyncSandbox
# sandbox = Sandbox("nextjs-developer-vercel-test") # Sync sandbox
# sandbox = await AsyncSandbox.create("nextjs-developer-vercel-test") # Async sandbox

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create('nextjs-developer-vercel-test')

team_id = "7512a1e9-06fd-4149-857e-28c025fbaacd"
start_cmd = "/compile_page.sh"
dockerfile = "e2b.Dockerfile"
template_name = "nextjs-developer-vercel-test"
template_id = "w83rwcbbai8nu9wxhcqx"
