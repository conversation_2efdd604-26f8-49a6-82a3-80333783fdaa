
import fs from "node:fs";
import { uploadToOSS } from '@/lib/core/upload-to-oss';
import { encryptFileLink } from "@/lib/core/file-link-encryption";
import { fail, success } from "@/lib/core/response";
import { UploadRes } from "../../model/uploadModel";
import { getFileCategory } from "@/lib/utils";

export async function POST(req: Request) {

    const formData = await req.formData();
    const file = formData.get('file');
    if (!file || typeof file === 'string') {
        return fail();
    }
    const filePath = `/tmp/${file.name}`;
    const arrayBuffer = await file.arrayBuffer();
    fs.writeFileSync(filePath, new Uint8Array(Buffer.from(arrayBuffer)));
    const fileLink = await uploadToOSS({ filePath });
    return success<UploadRes>({
        fileId: encryptFileLink(fileLink),
        fileName: file.name,
        fileType: getFileCategory(file.name),
    })

}

