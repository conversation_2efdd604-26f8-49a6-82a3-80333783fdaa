import Sandbox from "@e2b/code-interpreter";

export async function deploy_nextjs(sandboxId: string) {
    const sbx = await Sandbox.connect(sandboxId)
    sbx.setTimeout(5 * 60 * 1000) // 5 minutes in milliseconds
    console.log("start run deploy.sh in sandbox")
    await sbx.commands.run('/deploy.sh', {
        timeoutMs: 5 * 60 * 1000
    })
    const project_name = (await sbx.files.read('/home/<USER>/nextjs-app/juchats-artifacts.txt')).trim()
    return {
        name: project_name,
        url: `https://${project_name}.vercel.app`
    }
}
