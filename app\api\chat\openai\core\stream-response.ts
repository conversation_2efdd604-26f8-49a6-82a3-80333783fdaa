import log from '@/lib/logs';
import { LongRunningTaskOpenAI } from './LongRunningTaskOpenAI';
import { ChatCompletionChunk, ChatCompletionCreateParamsBase, ChatCompletionMessageParam } from 'openai/resources/chat/completions';
import OpenAI from 'openai';
import { getCompletion } from './completion';
import { Stream } from 'openai/streaming';
import { fail } from '@/lib/core/response';
import { HttpStatusCode } from 'axios';
import { getResponse } from '../../common/readable-stream';
import { browsing } from '@/lib/tools/search-online';
import { SEARCH_ERROR_MESSAGE } from '../../common/constants';

async function genSearchQuery(messages: ChatCompletionMessageParam[]) {
    const currentDateTime = new Date().toISOString()
    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_BASE_URL,
        dangerouslyAllowBrowser: true,
    });
    return await openai.chat.completions.create({
        model: 'gpt-4o-mini',
        stream: false,
        response_format: {
            type: "json_object",
        },
        messages: [
            {
                role: 'system',
                content: `
                You are a query generator,please generate a specific search query based on the user's input: ${messages[messages.length - 1]?.content} and the context of the conversation.Here is the context of the conversation:
                """
                ${messages.map(message => `${message.role}: ${message.content}`.replace(/<think>[\s\S]*?<\/think>/g, '')).join("\n")}
                """
                The current date is {${currentDateTime}}，If necessary, please refer to the current time for generating the query.
                The language of the searchQuery is determined by the user's language.
                Your output is a json object,with a key 'query',value is a query string.
                It is necessary to determine whether the user input is a question or not. If it is a question, generate a query and return it; if it is some greetings or asks "Who are you?", return an empty string in the 'query' field.
                for example:
                {
                    query: "What do you think of the location of this event?"
                }
                `,
            },
            {
                role: 'user',
                content: messages[messages.length - 1]?.content as string,
            },
        ],
    }).then((completion) => {
        const { content } = completion.choices[0].message;
        const parsed = JSON.parse(content || "{}");

        return parsed.query;
    });
}


export async function getStreamResponse(
    body: ChatCompletionCreateParamsBase,
    userID: string,
    apiKey: string,
) {

    try {

        const baseURL = body.model.startsWith("deepseek") ? process.env.DEEPSEEK_BASE_URL : process.env.OPENAI_BASE_URL
        console.log('baseURL---->', baseURL)
        console.log('apiKey---->', apiKey)
        const client = new OpenAI({
            apiKey,
            baseURL,
            dangerouslyAllowBrowser: true,
        });

        let completionResponse = await getCompletion(body, client) as Stream<ChatCompletionChunk>

        const task = new LongRunningTaskOpenAI({
            body, userID, client, response: completionResponse
        });

        return getResponse(task, userID);


    } catch (error: any) {
        log({
            type: "ERROR",
            message: {
                model: 'OpenAI',
                message: error,
                userID,
            },
        })
        return fail(error.message, HttpStatusCode.InternalServerError, HttpStatusCode.InternalServerError)

    }


}