import { TemplateId } from './templates'
import { ExecutionError, Result } from '@e2b/code-interpreter'

type ExecutionResultBase = {
  sbxId: string
}

export type ExecutionResultInterpreter = ExecutionResultBase & {
  template: 'custom-codeinterpreter' | 'code-interpreter-v1' | 'juchats-code-interpreter'
  stdout: string[]
  stderr: string[]
  runtimeError?: ExecutionError
  cellResults: Result[]
}

export type ExecutionResultWeb = ExecutionResultBase & {
  template: Exclude<TemplateId, 'custom-codeinterpreter' | 'code-interpreter-v1' | 'juchats-code-interpreter'>
  url: string
}

export type ExecutionResult = ExecutionResultInterpreter | ExecutionResultWeb
