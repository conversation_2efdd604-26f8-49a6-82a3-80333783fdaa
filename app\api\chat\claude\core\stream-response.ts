import { MessageCreateParamsBase, RawMessageStreamEvent } from "@anthropic-ai/sdk/resources/messages";
import { LongRunningTaskAnthropic } from "./LongRunningTaskAnthropic";
import Anthropic from "@anthropic-ai/sdk";
import { getCompletion } from "./completion";
import { Stream } from "@anthropic-ai/sdk/streaming";
import { fail } from "@/lib/core/response";
import { HttpStatusCode } from "axios";
import { getResponse } from "../../common/readable-stream";
import log from "@/lib/logs";


export async function getStreamResponse(
    body: MessageCreateParamsBase,
    userID: string,
    apiKey: string
) {
    try {

        const client = new Anthropic({
            apiKey,
            baseURL: process.env.ANTHROPIC_BASE_URL,
            dangerouslyAllowBrowser: true,
        });


        const completionResponse = await getCompletion(body, client, userID) as Stream<RawMessageStreamEvent>;

        const task = new LongRunningTaskAnthropic({
            body, userID, client, response: completionResponse
        });

        return getResponse(task, userID);

    } catch (error: any) {
        log({
            type: "ERROR",
            message: {
                model: '<PERSON>',
                message: error,
                userID,
            },
        })
        return fail(error, HttpStatusCode.InternalServerError, HttpStatusCode.InternalServerError)
    }

}