# This is a config for E2B sandbox template.
# You can use 'template_id' (rtnj5ynpykzr3jz95pmw) or 'template_name (streamlit-developer) from this config to spawn a sandbox:

# Python SDK
# from e2b import Sandbox
# sandbox = Sandbox(template='streamlit-developer')

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create({ template: 'streamlit-developer' })

template_id = "rtnj5ynpykzr3jz95pmw"
dockerfile = "e2b.Dockerfile"
template_name = "streamlit-developer"
start_cmd = "cd /home/<USER>"
cpu_count = 4
memory_mb = 4_096
