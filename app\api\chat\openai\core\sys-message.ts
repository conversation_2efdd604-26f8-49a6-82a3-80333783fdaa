import { ChatCompletionMessageParam } from "openai/resources/chat/completions";
import { generateCommonSysPrompt, DEEPSEEK_SYS_PROMPT } from "../../common/prompt-instruction";
export function getSystemMessage(model: string, messages: ChatCompletionMessageParam[]) {
    console.log('model---->', model);
    // 直接定义一个固定的系统消息内容
    let searchSystemPrompt = `
    ${generateCommonSysPrompt(model)}
    ## MUST return well-formatted and clearly-structured perfect Markdown format, and generate articles in the best and most professional way (with clear logic and in-depth explanations for professional content).
    `;

    if (model.startsWith('deepseek')) {
        searchSystemPrompt = DEEPSEEK_SYS_PROMPT;
    }

    if (messages.filter((message: ChatCompletionMessageParam) => message.role === 'system').length === 0) {
        // 如果没有找到系统消息，则在数组开头插入一条新的系统消息
        messages.unshift({
            role: 'system',
            content: searchSystemPrompt,
        });
    }
    // if (model.startsWith('o1-') || model.startsWith('o3-')) {
    //     messages = messages.map((message: ChatCompletionMessageParam) => {
    //         return {
    //             ...message,
    //             role: message.role === 'system' ? 'developer' : message.role
    //         } as ChatCompletionMessageParam;
    //     });
    // }
    return messages;
}

